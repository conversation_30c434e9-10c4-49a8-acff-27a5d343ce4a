import React from 'react'
import { Check<PERSON>ircle, XCircle, Clock, AlertCircle, Loader2 } from 'lucide-react'

const StatusIndicator = ({ status, size = 'md', showText = true, className = '' }) => {
  const sizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  }

  const textSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  const configs = {
    success: {
      icon: CheckCircle,
      color: 'text-success-600',
      bgColor: 'bg-success-100',
      text: 'Success'
    },
    error: {
      icon: XCircle,
      color: 'text-danger-600',
      bgColor: 'bg-danger-100',
      text: 'Error'
    },
    warning: {
      icon: AlertCircle,
      color: 'text-warning-600',
      bgColor: 'bg-warning-100',
      text: 'Warning'
    },
    pending: {
      icon: Clock,
      color: 'text-warning-600',
      bgColor: 'bg-warning-100',
      text: 'Pending'
    },
    loading: {
      icon: Loader2,
      color: 'text-primary-600',
      bgColor: 'bg-primary-100',
      text: 'Loading',
      animate: true
    },
    info: {
      icon: AlertCircle,
      color: 'text-primary-600',
      bgColor: 'bg-primary-100',
      text: 'Info'
    }
  }

  const config = configs[status] || configs.info
  const Icon = config.icon

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`rounded-full p-1 ${config.bgColor}`}>
        <Icon 
          className={`${sizes[size]} ${config.color} ${config.animate ? 'animate-spin' : ''}`} 
        />
      </div>
      {showText && (
        <span className={`${textSizes[size]} font-medium ${config.color}`}>
          {config.text}
        </span>
      )}
    </div>
  )
}

export default StatusIndicator
