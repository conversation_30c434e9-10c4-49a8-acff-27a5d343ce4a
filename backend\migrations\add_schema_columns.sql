-- Add schema columns to plants table for cross-schema support
-- This allows each plant to specify different schemas for CSDOCS and metadata tables

ALTER TABLE plants ADD COLUMN csdocs_schema VARCHAR(128);
ALTER TABLE plants ADD COLUMN metadata_schema VARCHAR(128);

-- Add comments for documentation
COMMENT ON COLUMN plants.csdocs_schema IS 'Oracle schema containing CSDOCS table (e.g., DOCUMENTS, FILESTORE)';
COMMENT ON COLUMN plants.metadata_schema IS 'Oracle schema containing CSMETA_* tables (e.g., METADATA, BUSINESS)';

-- Update existing plants with default schema values (can be updated later)
UPDATE plants SET csdocs_schema = username WHERE csdocs_schema IS NULL;
UPDATE plants SET metadata_schema = username WHERE metadata_schema IS NULL;
