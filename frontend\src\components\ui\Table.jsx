import React from 'react'

const Table = ({ children, className = '', responsive = true }) => {
  return (
    <div className={`overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg ${responsive ? 'overflow-x-auto' : ''}`}>
      <table className={`table ${className}`}>
        {children}
      </table>
    </div>
  )
}

const TableHeader = ({ children, className = '' }) => {
  return (
    <thead className={`table-header ${className}`}>
      {children}
    </thead>
  )
}

const TableBody = ({ children, className = '' }) => {
  return (
    <tbody className={`table-body ${className}`}>
      {children}
    </tbody>
  )
}

const TableRow = ({ children, className = '' }) => {
  return (
    <tr className={className}>
      {children}
    </tr>
  )
}

const TableHeaderCell = ({ children, className = '' }) => {
  return (
    <th className={`table-header-cell ${className}`}>
      {children}
    </th>
  )
}

const TableCell = ({ children, className = '' }) => {
  return (
    <td className={`table-cell ${className}`}>
      {children}
    </td>
  )
}

Table.Header = TableHeader
Table.Body = TableBody
Table.Row = TableRow
Table.HeaderCell = TableHeaderCell
Table.Cell = TableCell

export default Table
