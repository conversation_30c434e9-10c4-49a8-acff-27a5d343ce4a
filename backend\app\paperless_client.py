import requests

def _headers(token=None):
    h = {}
    if token:
        h['Authorization'] = f"Token {token}"
    return h

def ensure_entities(api_url, token, payload):
    # placeholder: create document types/tags if needed
    return {}

def test_paperless_api(api_url, token):
    """Test connection to Paperless-NGX API"""
    try:
        # Clean up API URL
        api_url = api_url.rstrip('/')

        # Test basic API connectivity
        headers = _headers(token)

        # Try to get API info/version
        response = requests.get(f"{api_url}/", headers=headers, timeout=10)

        if response.status_code == 200:
            api_info = response.json()
            return {
                'success': True,
                'version': api_info.get('version', 'Unknown'),
                'message': 'Connection successful'
            }
        elif response.status_code == 401:
            return {
                'success': False,
                'error': 'Authentication failed - check your token',
                'status_code': response.status_code
            }
        else:
            return {
                'success': False,
                'error': f'API returned status {response.status_code}',
                'status_code': response.status_code
            }

    except requests.exceptions.Timeout:
        return {
            'success': False,
            'error': 'Connection timeout - check API URL and network connectivity'
        }
    except requests.exceptions.ConnectionError:
        return {
            'success': False,
            'error': 'Connection failed - check API URL and ensure Paperless is running'
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }

def get_paperless_fields_from_api(api_url, token):
    """Get custom fields from Paperless-NGX API"""
    try:
        api_url = api_url.rstrip('/')
        headers = _headers(token)

        # Try to get custom fields (this endpoint may vary by Paperless version)
        response = requests.get(f"{api_url}/custom_fields/", headers=headers, timeout=10)

        custom_fields = []
        if response.status_code == 200:
            fields_data = response.json()

            # Handle paginated response
            if isinstance(fields_data, dict) and 'results' in fields_data:
                fields_data = fields_data['results']

            for field in fields_data:
                custom_fields.append({
                    'name': field.get('name', f"custom_field_{field.get('id')}"),
                    'type': field.get('data_type', 'text'),
                    'required': field.get('required', False),
                    'description': field.get('description', ''),
                    'is_custom': True,
                    'exists': True,
                    'id': field.get('id')
                })

        return custom_fields

    except Exception as e:
        print(f"Error fetching custom fields: {e}")
        return []

def create_custom_field(api_url, token, field_data):
    """Create a new custom field in Paperless-NGX"""
    print(field_data)
    try:
        api_url = api_url.rstrip('/')
        headers = _headers(token)
        headers['Content-Type'] = 'application/json'

        response = requests.post(f"{api_url}/custom_fields/",
                               json=field_data,
                               headers=headers,
                               timeout=10)

        if response.status_code in [200, 201]:
            return {
                'success': True,
                'field': response.json(),
                'message': 'Custom field created successfully'
            }
        else:
            return {
                'success': False,
                'error': f'Failed to create custom field: {response.status_code}',
                'details': response.text
            }

    except Exception as e:
        return {
            'success': False,
            'error': f'Error creating custom field: {str(e)}'
        }

def get_document_types(api_url, token):
    """Get document types from Paperless-NGX"""
    try:
        api_url = api_url.rstrip('/')
        headers = _headers(token)

        response = requests.get(f"{api_url}/document_types/", headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'results' in data:
                return data['results']
            return data
        return []

    except Exception as e:
        print(f"Error fetching document types: {e}")
        return []

def get_correspondents(api_url, token):
    """Get correspondents from Paperless-NGX"""
    try:
        api_url = api_url.rstrip('/')
        headers = _headers(token)

        response = requests.get(f"{api_url}/correspondents/", headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'results' in data:
                return data['results']
            return data
        return []

    except Exception as e:
        print(f"Error fetching correspondents: {e}")
        return []

def get_tags(api_url, token):
    """Get tags from Paperless-NGX"""
    try:
        api_url = api_url.rstrip('/')
        headers = _headers(token)

        response = requests.get(f"{api_url}/tags/", headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'results' in data:
                return data['results']
            return data
        return []

    except Exception as e:
        print(f"Error fetching tags: {e}")
        return []
