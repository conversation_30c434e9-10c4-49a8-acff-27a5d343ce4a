import React, { useState, useEffect, useRef } from 'react'
import { useStore } from '../store/useStore'
import { api } from '../lib/api'
import { Factory, ChevronDown, Check, Plus } from 'lucide-react'
import { But<PERSON>, LoadingSpinner } from './ui'

const PlantSelector = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [plants, setPlants] = useState([])
  const [loading, setLoading] = useState(false)
  const dropdownRef = useRef(null)
  
  const { selectedPlant, setSelectedPlant } = useStore()

  useEffect(() => {
    loadPlants()
    // Close dropdown when clicking outside
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  async function loadPlants() {
    setLoading(true)
    try {
      const response = await api.get('/plants')
      setPlants(response.data)
    } catch (error) {
      console.error('Failed to load plants:', error)
    } finally {
      setLoading(false)
    }
  }

  function handleSelectPlant(plant) {
    setSelectedPlant(plant)
    setIsOpen(false)
  }

  function handleAddPlant() {
    setIsOpen(false)
    window.location.href = '/plants'
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-full flex items-center justify-between px-4 py-3 text-left bg-white border border-gray-300 rounded-lg
          hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500
          transition-colors duration-200
          ${selectedPlant ? 'text-gray-900' : 'text-gray-500'}
        `}
      >
        <div className="flex items-center space-x-3 min-w-0 flex-1">
          <div className={`
            w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0
            ${selectedPlant ? 'bg-primary-100' : 'bg-gray-100'}
          `}>
            <Factory className={`w-4 h-4 ${selectedPlant ? 'text-primary-600' : 'text-gray-400'}`} />
          </div>
          <div className="min-w-0 flex-1">
            {selectedPlant ? (
              <div>
                <div className="font-medium text-gray-900 truncate">{selectedPlant.name}</div>
                <div className="text-xs text-gray-500 truncate">
                  {selectedPlant.host}:{selectedPlant.port}
                </div>
              </div>
            ) : (
              <span className="text-gray-500">Select a plant...</span>
            )}
          </div>
        </div>
        <ChevronDown className={`
          w-4 h-4 text-gray-400 transition-transform duration-200 flex-shrink-0
          ${isOpen ? 'transform rotate-180' : ''}
        `} />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <LoadingSpinner size="sm" />
              <span className="ml-2 text-sm text-gray-600">Loading plants...</span>
            </div>
          ) : plants.length === 0 ? (
            <div className="p-4 text-center">
              <Factory className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-3">No plants configured</p>
              <Button
                size="sm"
                onClick={handleAddPlant}
                className="flex items-center space-x-1"
              >
                <Plus className="w-3 h-3" />
                <span>Add Plant</span>
              </Button>
            </div>
          ) : (
            <>
              {plants.map((plant) => (
                <button
                  key={plant.id}
                  onClick={() => handleSelectPlant(plant)}
                  className={`
                    w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50
                    transition-colors duration-150
                    ${selectedPlant?.id === plant.id ? 'bg-primary-50' : ''}
                  `}
                >
                  <div className={`
                    w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0
                    ${selectedPlant?.id === plant.id ? 'bg-primary-100' : 'bg-gray-100'}
                  `}>
                    <Factory className={`
                      w-4 h-4 
                      ${selectedPlant?.id === plant.id ? 'text-primary-600' : 'text-gray-400'}
                    `} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-gray-900 truncate">{plant.name}</div>
                    <div className="text-xs text-gray-500 truncate">
                      {plant.host}:{plant.port}
                    </div>
                  </div>
                  {selectedPlant?.id === plant.id && (
                    <Check className="w-4 h-4 text-primary-600 flex-shrink-0" />
                  )}
                </button>
              ))}
              <div className="border-t border-gray-200 p-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleAddPlant}
                  className="w-full flex items-center justify-center space-x-1"
                >
                  <Plus className="w-3 h-3" />
                  <span>Add New Plant</span>
                </Button>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  )
}

export default PlantSelector
