from pydantic_settings import BaseSettings
class Settings(BaseSettings):
    database_url: str = "sqlite:///./data/app.db"
    fernet_secret: str | None = None
    demo_mode: bool = False
    redis_broker: str = "redis://redis:6379/0"
    paperless_api_url: str | None = None
    paperless_api_token: str | None = None
    batch_size: int = 100
    retry_limit: int = 3
    class Config:
        env_file = ".env"
settings = Settings()
