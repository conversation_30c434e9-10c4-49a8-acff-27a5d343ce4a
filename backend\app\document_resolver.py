"""
Document path resolver for Docker mounted storage
Handles conversion between PP database paths and Docker mount paths
"""

import os
import logging
from pathlib import Path
from typing import Optional, Tuple

logger = logging.getLogger(__name__)

class DocumentPathResolver:
    """
    Resolves document paths from PP database to Docker mounted storage
    
    PP Database Path Examples:
    - \\server\share\plant1\docs\file.pdf
    - /opt/pp/plant1/documents/file.pdf
    - C:\PP\Plant1\Docs\file.pdf
    
    Docker Mount Path Examples:
    - /Plant1/docs/file.pdf
    - /Plant2/documents/file.pdf
    """
    
    def __init__(self, plant_storage_path: str):
        """
        Initialize resolver with plant's Docker mount path
        
        Args:
            plant_storage_path: Docker mount path (e.g., "/Plant1", "/Plant2")
        """
        self.plant_storage_path = plant_storage_path.rstrip('/')
        
    def resolve_document_path(self, pp_docpath: str) -> Tuple[str, bool]:
        """
        Convert PP database document path to Docker mount path
        
        Args:
            pp_docpath: Document path from PP database (CSDOC.DOCPATH)
            
        Returns:
            Tuple of (resolved_path, file_exists)
        """
        if not pp_docpath:
            return "", False
            
        # Normalize path separators
        normalized_path = pp_docpath.replace('\\', '/')
        
        # Extract relative path from various PP path formats
        relative_path = self._extract_relative_path(normalized_path)
        
        # Build Docker mount path
        docker_path = f"{self.plant_storage_path}/{relative_path}".replace('//', '/')
        
        # Check if file exists
        file_exists = os.path.exists(docker_path)
        
        logger.debug(f"Resolved path: {pp_docpath} -> {docker_path} (exists: {file_exists})")
        
        return docker_path, file_exists
    
    def _extract_relative_path(self, normalized_path: str) -> str:
        """
        Extract relative path from PP database path
        
        Common PP path patterns:
        - \\server\share\plant1\docs\file.pdf -> docs/file.pdf
        - /opt/pp/plant1/documents/file.pdf -> documents/file.pdf
        - C:\PP\Plant1\Docs\file.pdf -> Docs/file.pdf
        """
        # Remove leading slashes and drive letters
        path = normalized_path.lstrip('/')
        if ':' in path:
            path = path.split(':', 1)[1].lstrip('/')
        
        # Split path components
        parts = [p for p in path.split('/') if p]
        
        if not parts:
            return ""
        
        # Try to find plant-specific directory and extract everything after it
        plant_indicators = ['plant', 'Plant', 'PLANT']
        
        for i, part in enumerate(parts):
            # Check if this part looks like a plant directory
            if any(indicator in part for indicator in plant_indicators):
                # Return everything after the plant directory
                if i + 1 < len(parts):
                    return '/'.join(parts[i + 1:])
                else:
                    return ""
        
        # If no plant directory found, try common patterns
        common_roots = ['share', 'data', 'documents', 'files', 'pp', 'PP']
        
        for i, part in enumerate(parts):
            if part.lower() in [root.lower() for root in common_roots]:
                # Skip the root and next directory (likely plant name)
                if i + 2 < len(parts):
                    return '/'.join(parts[i + 2:])
                elif i + 1 < len(parts):
                    return '/'.join(parts[i + 1:])
        
        # Fallback: assume last 2-3 components are the actual file path
        if len(parts) >= 2:
            return '/'.join(parts[-2:])
        else:
            return '/'.join(parts)
    
    def get_paperless_import_path(self, pp_docpath: str) -> Optional[str]:
        """
        Get the path that should be used for Paperless document import
        
        Args:
            pp_docpath: Document path from PP database
            
        Returns:
            Path for Paperless import, or None if file doesn't exist
        """
        docker_path, exists = self.resolve_document_path(pp_docpath)
        
        if not exists:
            logger.warning(f"Document not found: {pp_docpath} -> {docker_path}")
            return None
            
        return docker_path
    
    def validate_storage_mount(self) -> bool:
        """
        Validate that the plant storage path is properly mounted
        
        Returns:
            True if storage path exists and is accessible
        """
        if not self.plant_storage_path:
            return False
            
        try:
            return os.path.exists(self.plant_storage_path) and os.path.isdir(self.plant_storage_path)
        except Exception as e:
            logger.error(f"Error validating storage mount {self.plant_storage_path}: {e}")
            return False
    
    def get_storage_stats(self) -> dict:
        """
        Get storage statistics for the plant mount
        
        Returns:
            Dictionary with storage information
        """
        if not self.validate_storage_mount():
            return {
                'mounted': False,
                'path': self.plant_storage_path,
                'error': 'Storage path not accessible'
            }
        
        try:
            stat = os.statvfs(self.plant_storage_path)
            total_space = stat.f_frsize * stat.f_blocks
            free_space = stat.f_frsize * stat.f_available
            used_space = total_space - free_space
            
            return {
                'mounted': True,
                'path': self.plant_storage_path,
                'total_gb': round(total_space / (1024**3), 2),
                'used_gb': round(used_space / (1024**3), 2),
                'free_gb': round(free_space / (1024**3), 2),
                'usage_percent': round((used_space / total_space) * 100, 1) if total_space > 0 else 0
            }
        except Exception as e:
            return {
                'mounted': True,
                'path': self.plant_storage_path,
                'error': f'Could not get storage stats: {e}'
            }

def create_resolver(plant) -> DocumentPathResolver:
    """
    Factory function to create a DocumentPathResolver for a plant
    
    Args:
        plant: Plant model instance
        
    Returns:
        DocumentPathResolver instance
    """
    storage_path = plant.storage_path or f"/Plant{plant.id}"
    return DocumentPathResolver(storage_path)
