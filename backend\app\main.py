from fastapi import FastAP<PERSON>, Depends, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from .database import engine, Base, get_db
from . import models, crud, schemas
from sqlalchemy.orm import Session
from .celery_tasks import migrate_plant_task, retry_log_task
from collections import defaultdict

Base.metadata.create_all(bind=engine)
app = FastAPI(title='pp_to_paperless v8.3 (merged)')
app.add_middleware(CORSMiddleware, allow_origins=['*'], allow_credentials=True, allow_methods=['*'], allow_headers=['*'])
@app.get('/')
def root(): return {'status':'ok'}

@app.get('/plants', response_model=list[schemas.PlantOut])
def list_plants(db: Session = Depends(get_db)):
    return crud.get_plants(db)

@app.post('/plants', response_model=schemas.PlantOut)
def add_plant(plant: schemas.PlantCreate, db: Session = Depends(get_db)):
    return crud.create_plant(db, plant)

@app.get('/plants/{plant_id}', response_model=schemas.PlantOut)
def get_plant(plant_id: int, db: Session = Depends(get_db)):
    plant = crud.get_plant(db, plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    return plant

@app.put('/plants/{plant_id}', response_model=schemas.PlantOut)
def update_plant(plant_id: int, plant_update: schemas.PlantUpdate, db: Session = Depends(get_db)):
    updated_plant = crud.update_plant(db, plant_id, plant_update)
    if not updated_plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    return updated_plant

@app.delete('/plants/{plant_id}')
def delete_plant(plant_id: int, db: Session = Depends(get_db)):
    success = crud.delete_plant(db, plant_id)
    if not success:
        raise HTTPException(status_code=404, detail="Plant not found")
    return {"message": "Plant deleted successfully"}

@app.post('/plants/{plant_id}/test-connection')
def test_plant_connection(plant_id: int, db: Session = Depends(get_db)):
    """Test database connection for a plant"""
    plant = crud.get_plant(db, plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    try:
        from .oracle_helper import connect_plant

        # Test connection
        conn = connect_plant(plant)
        cursor = conn.cursor()

        # Simple test query
        cursor.execute("SELECT 1 FROM DUAL")
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        return {
            "success": True,
            "message": "Database connection successful",
            "plant_id": plant_id,
            "plant_name": plant.name
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"Database connection failed: {str(e)}",
            "plant_id": plant_id,
            "plant_name": plant.name
        }

@app.get('/plants/{plant_id}/schemas')
def discover_plant_schemas(plant_id: int, db: Session = Depends(get_db)):
    """Discover all schemas and their tables for a plant"""
    plant = crud.get_plant(db, plant_id)
    if not plant: raise HTTPException(status_code=404, detail="Plant not found")

    try:
        from .oracle_helper import connect_plant
        from .config import settings

        # In demo mode, return mock schema data
        # if settings.demo_mode:
        #     return {
        #         'plant_id': plant_id,
        #         'plant_name': plant.name,
        #         'schemas': [
        #             {
        #                 'schema_name': 'DOCUMENTS',
        #                 'table_count': 2,
        #                 'tables': [
        #                     {'table_name': 'CSDOCS', 'table_type': 'TABLE', 'comments': 'Document master table'},
        #                     {'table_name': 'CSDOC_ARCHIVE', 'table_type': 'TABLE', 'comments': 'Archived documents'}
        #                 ]
        #             },
        #             {
        #                 'schema_name': 'BUSINESS',
        #                 'table_count': 8,
        #                 'tables': [
        #                     {'table_name': 'CSMETA_PROJECT', 'table_type': 'TABLE', 'comments': 'Project metadata'},
        #                     {'table_name': 'CSMETA_EQUIP', 'table_type': 'TABLE', 'comments': 'Equipment metadata'},
        #                     {'table_name': 'CSMETA_SAFETY', 'table_type': 'TABLE', 'comments': 'Safety documentation'},
        #                     {'table_name': 'CSMETA_MAINT', 'table_type': 'TABLE', 'comments': 'Maintenance records'},
        #                     {'table_name': 'CSMETA_QUALITY', 'table_type': 'TABLE', 'comments': 'Quality control data'},
        #                     {'table_name': 'CSMETA_TRAINING', 'table_type': 'TABLE', 'comments': 'Training documentation'},
        #                     {'table_name': 'CSMETA_VENDOR', 'table_type': 'TABLE', 'comments': 'Vendor information'},
        #                     {'table_name': 'CSMETA_COMPLIANCE', 'table_type': 'TABLE', 'comments': 'Compliance records'}
        #                 ]
        #             },
        #             {
        #                 'schema_name': 'SYSTEM',
        #                 'table_count': 3,
        #                 'tables': [
        #                     {'table_name': 'USER_PROFILES', 'table_type': 'TABLE', 'comments': 'User profile data'},
        #                     {'table_name': 'AUDIT_LOG', 'table_type': 'TABLE', 'comments': 'System audit trail'},
        #                     {'table_name': 'CONFIG_PARAMS', 'table_type': 'TABLE', 'comments': 'System configuration'}
        #                 ]
        #             }
        #         ]
        #     }

        # Real Oracle connection - discover all schemas and tables
        conn = connect_plant(plant)
        cursor = conn.cursor()

        # Query to get all schemas accessible to this user with relevant tables
        

        
        schemas_map = defaultdict(lambda: {"schema_name": "", "tables": []})
        cursor.execute("""
            SELECT OWNER, TABLE_NAME
            FROM ALL_TAB_COLUMNS
            WHERE COLUMN_NAME = 'DOCNAME'
        """)
        doc_tables = cursor.fetchall()

        # 2️⃣ — Parcourir chaque table source
        for source_owner, source_table in doc_tables:
            try:
                # --- Récupérer infos table source
                cursor.execute("""
                    SELECT
                        table_name,
                        'TABLE' AS table_type,
                        NVL(comments, 'No description') AS comments
                    FROM all_tab_comments
                    WHERE owner = :schema AND table_name = :table_name
                """, {'schema': source_owner, 'table_name': source_table})

                result = cursor.fetchone()
                if result:
                    table_name, table_type, comments = result
                else:
                    table_name, table_type, comments = source_table, 'TABLE', 'Not found in all_tab_comments'

                # Ajouter la table source à son schéma
                schemas_map[source_owner]["schema_name"] = source_owner
                schemas_map[source_owner]["tables"].append({
                    "table_name": table_name,
                    "table_type": table_type,
                    "comments": comments
                })

                # --- Exécuter la 2ᵉ requête pour trouver les tables référencées
                query = f"""
                    SELECT DISTINCT tt.owner, ct.tablename
                    FROM "{source_owner}"."{source_table}" ct
                    LEFT JOIN all_tables tt ON tt.table_name = ct.tablename
                    WHERE tt.owner IS NOT NULL
                """
                cursor.execute(query)
                ref_tables = cursor.fetchall()

                for ref_owner, ref_table_name in ref_tables:
                    cursor.execute("""
                        SELECT
                            table_name,
                            'TABLE' AS table_type,
                            NVL(comments, 'No description') AS comments
                        FROM all_tab_comments
                        WHERE owner = :schema AND table_name = :table_name
                    """, {'schema': ref_owner, 'table_name': ref_table_name})

                    result = cursor.fetchone()
                    if result:
                        t_name, t_type, t_comments = result
                    else:
                        t_name, t_type, t_comments = ref_table_name, 'TABLE', 'Not found in all_tab_comments'

                    # Ajouter la table référencée dans son schéma correspondant
                    schemas_map[ref_owner]["schema_name"] = ref_owner
                    schemas_map[ref_owner]["tables"].append({
                        "table_name": t_name,
                        "table_type": t_type,
                        "comments": t_comments
                    })

            except cx_Oracle.DatabaseError as e:
                print(f"Erreur sur {source_owner}.{source_table}: {e}")
        # 3️⃣ — Finaliser les données au bon format
        schemas_data = []
        for schema_name, data in schemas_map.items():
            tables = data["tables"]
            schema_entry = {
                "schema_name": schema_name,
                "table_count": len(tables),
                "tables": tables
            }
            schemas_data.append(schema_entry)
        cursor.close()
        conn.close()

        return {
            'plant_id': plant_id,
            'plant_name': plant.name,
            'schemas': schemas_data
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Schema discovery error: {str(e)}")

@app.get('/metadata/tables/{plant_id}')
def metadata_tables(plant_id: int, db: Session = Depends(get_db)):
    """Auto-discover metadata tables from PP Oracle database"""
    plant = crud.get_plant(db, plant_id)
    if not plant: raise HTTPException(status_code=404)

    try:
        from .oracle_helper import connect_plant
        from .config import settings

        # In demo mode, return mock tables
        # if settings.demo_mode:
        #     return [
        #         {'table_name': 'CSDOCS', 'description': 'Document master table'},
        #         {'table_name': 'CSMETA_PROJECT', 'description': 'Project metadata'},
        #         {'table_name': 'CSMETA_EQUIP', 'description': 'Equipment metadata'},
        #         {'table_name': 'CSMETA_SAFETY', 'description': 'Safety documentation'},
        #         {'table_name': 'CSMETA_MAINT', 'description': 'Maintenance records'}
        #     ]

        # Real Oracle connection - discover tables across schemas
        conn = connect_plant(plant)
        cursor = conn.cursor()

        tables = []

        # Get schema names (default to username if not specified)
        csdocs_schema = plant.csdocs_schema or plant.username
        metadata_schema = plant.metadata_schema.split(',') or plant.username


        # Query CSDOCS table from its schema
        try:
            cursor.execute("""
                SELECT table_name, comments
                FROM all_tab_comments
                WHERE owner = :schema AND table_name LIKE 'CSDOC%'
                ORDER BY table_name
            """, {'schema': csdocs_schema.upper()})

            for row in cursor.fetchall():
                tables.append({
                    'table_name': row[0],
                    'description': row[1] or f'Document table {row[0]}',
                    'schema': csdocs_schema
                })
        except Exception as e:
            print(f"Warning: Could not query CSDOCS schema {csdocs_schema}: {e}")

        # Query metadata tables from their schema
        for md_schema in metadata_schema :
            print('Schema %s' %md_schema)
            try:
                cursor.execute("""
                    SELECT table_name, comments
                    FROM all_tab_comments
                    WHERE owner = :schema 
                    ORDER BY table_name
                """, {'schema': md_schema.upper()})

                for row in cursor.fetchall():
                    tables.append({
                        'table_name': row[0],
                        'description': row[1] or f'Metadata table {row[0]}',
                        'schema': md_schema
                    })
            except Exception as e:
                print(f"Warning: Could not query metadata schema {md_schema}: {e}")
        # print(tables)
        cursor.close()
        conn.close()

        return tables

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.get('/metadata/columns/{plant_id}/{table_schema}/{table_name}')
def metadata_columns(plant_id: int, table_schema: str, table_name: str, db: Session = Depends(get_db)):
    """Auto-discover columns from PP metadata table"""
    plant = crud.get_plant(db, plant_id)
    if not plant: raise HTTPException(status_code=404)

    try:
        from .oracle_helper import connect_plant
        from .config import settings

        # In demo mode, return mock columns based on table
        # if settings.demo_mode:
        #     mock_columns = {
        #         'CSDOCS': [
        #             {'column_name': 'DKEY', 'data_type': 'NUMBER', 'nullable': 'N', 'description': 'Document key'},
        #             {'column_name': 'RECORDKEY', 'data_type': 'VARCHAR2', 'nullable': 'N', 'description': 'Reference key'},
        #             {'column_name': 'DOCNAME', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Document name'},
        #             {'column_name': 'DOCPATH', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'File path'},
        #             {'column_name': 'FILESIZE', 'data_type': 'NUMBER', 'nullable': 'Y', 'description': 'File size in bytes'},
        #             {'column_name': 'CREATED_DATE', 'data_type': 'DATE', 'nullable': 'Y', 'description': 'Creation date'}
        #         ],
        #         'CSMETA_PROJECT': [
        #             {'column_name': 'RECORDKEY', 'data_type': 'VARCHAR2', 'nullable': 'N', 'description': 'Reference key'},
        #             {'column_name': 'PROJ_ID', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Project ID'},
        #             {'column_name': 'PROJ_NAME', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Project name'},
        #             {'column_name': 'PROJ_DESC', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Project description'},
        #             {'column_name': 'PROJ_MANAGER', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Project manager'},
        #             {'column_name': 'PROJ_STATUS', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Project status'}
        #         ],
        #         'CSMETA_EQUIP': [
        #             {'column_name': 'RECORDKEY', 'data_type': 'VARCHAR2', 'nullable': 'N', 'description': 'Reference key'},
        #             {'column_name': 'EQUIP_ID', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Equipment ID'},
        #             {'column_name': 'EQUIP_NAME', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Equipment name'},
        #             {'column_name': 'EQUIP_TYPE', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Equipment type'},
        #             {'column_name': 'LOCATION', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Equipment location'},
        #             {'column_name': 'MANUFACTURER', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Manufacturer'}
        #         ]
        #     }

        #     return mock_columns.get(table_name, [
        #         {'column_name': 'RECORDKEY', 'data_type': 'VARCHAR2', 'nullable': 'N', 'description': 'Reference key'},
        #         {'column_name': 'FIELD1', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Custom field 1'},
        #         {'column_name': 'FIELD2', 'data_type': 'VARCHAR2', 'nullable': 'Y', 'description': 'Custom field 2'}
        #     ])

        # Real Oracle connection - discover columns across schemas
        conn = connect_plant(plant)
        cursor = conn.cursor()

        # Determine which schema the table is in
        table_schema_ = None
        csdocs_schema = plant.csdocs_schema or plant.username

        if table_name.upper().startswith('CSDOC'):
            table_schema_ = csdocs_schema
        # elif table_name.upper().startswith('CSMETA'):
        #     table_schema = metadata_schema
        else:
            # Default to metadata schema for unknown tables
            table_schema_ = table_schema

        # Query to get column information with comments from the appropriate schema
        cursor.execute("""
            SELECT
                c.column_name,
                c.data_type,
                c.nullable,
                cc.comments
            FROM all_tab_columns c
            LEFT JOIN all_col_comments cc ON c.owner = cc.owner AND c.table_name = cc.table_name AND c.column_name = cc.column_name
            WHERE c.owner = :schema AND c.table_name = :table_name
            ORDER BY c.column_id
        """, {'schema': table_schema_.upper(), 'table_name': table_name.upper()})

        columns = []
        for row in cursor.fetchall():
            columns.append({
                'column_name': row[0],
                'data_type': row[1],
                'nullable': row[2],
                'description': row[3] or f'Column {row[0]}'
            })

        cursor.close()
        conn.close()

        return columns

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.get('/metadata/preview/{plant_id}/{table_schema}/{table_name}')
def get_metadata_preview(plant_id: int, table_schema: str,table_name: str, limit: int = 100, db: Session = Depends(get_db)):
    """Get preview data (first N rows) from a metadata table"""
    try:
        plant = crud.get_plant(db, plant_id)
        if not plant:
            raise HTTPException(status_code=404, detail="Plant not found")
        from .oracle_helper import connect_plant
        from .config import settings
        # Demo mode - return mock data
        # if settings.demo_mode:
        #     mock_data = []
        #     for i in range(min(limit, 25)):  # Return up to 25 mock rows
        #         mock_data.append({
        #             'DKEY': 1000 + i,
        #             'PROJECT_NAME': f'Project {chr(65 + i % 26)}',
        #             'PROJECT_DESC': f'Description for project {chr(65 + i % 26)}',
        #             'PROJECT_TYPE': ['Engineering', 'Maintenance', 'Safety', 'Quality'][i % 4],
        #             'PROJECT_STATUS': ['Active', 'Completed', 'On Hold', 'Cancelled'][i % 4],
        #             'CREATED_DATE': f'2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}',
        #             'MODIFIED_DATE': f'2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}'
        #         })
        #     return {
        #         'data': mock_data,
        #         'total_rows': len(mock_data),
        #         'columns': ['DKEY', 'PROJECT_NAME', 'PROJECT_DESC', 'PROJECT_TYPE', 'PROJECT_STATUS', 'CREATED_DATE', 'MODIFIED_DATE'],
        #         'table_name': table_name,
        #         'demo_mode': True
        #     }

        # Real mode - query Oracle database
        conn = connect_plant(plant)
        cursor = conn.cursor()

        # Determine which schema the table is in
        table_schema_ = None
        csdocs_schema = plant.csdocs_schema or plant.username
        metadata_schema = plant.metadata_schema or plant.username

        if table_name.upper().startswith('CSDOC'):
            table_schema_ = csdocs_schema

        else:
            # Default to metadata schema for unknown tables
            table_schema_ = table_schema

        # Get column names first
        cursor.execute("""
            SELECT column_name
            FROM all_tab_columns
            WHERE owner = :schema AND table_name = :table_name
            ORDER BY column_id
        """, {'schema': table_schema_.upper(), 'table_name': table_name.upper()})

        columns = [row[0] for row in cursor.fetchall()]

        if not columns:
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Table {table_name} not found or no columns")

        # Query preview data
        column_list = ', '.join(columns)
        query = f"""
            SELECT {column_list}
            FROM {table_schema_}.{table_name}
            WHERE ROWNUM <= :limit
            ORDER BY ROWNUM
        """

        cursor.execute(query, {'limit': limit})
        rows = cursor.fetchall()

        # Convert to list of dictionaries
        data = []
        for row in rows:
            row_dict = {}
            for i, col_name in enumerate(columns):
                value = row[i]
                # Convert Oracle types to JSON-serializable types
                if hasattr(value, 'isoformat'):  # Date/DateTime
                    value = value.isoformat()
                elif value is None:
                    value = None
                else:
                    value = str(value)
                row_dict[col_name] = value
            data.append(row_dict)

        cursor.close()
        conn.close()

        return {
            'data': data,
            'total_rows': len(data),
            'columns': columns,
            'table_name': table_name,
            'schema': table_schema_
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.post('/mappings')
def save_mapping(mapping: schemas.MappingIn, db: Session = Depends(get_db)):
    """Save enhanced mapping with tags as IDs, extra fields, and default values"""
    m = crud.save_enhanced_mapping(db, mapping)
    return {
        'id': m.id,
        'message': 'Enhanced mapping saved successfully!',
        'features': {
            'tag_mapping_by_id': True,
            'extra_fields_supported': True,
            'default_values_supported': True
        }
    }

@app.get('/mappings/{plant_id}')
def get_mappings(plant_id: int, db: Session = Depends(get_db)):
    """Get enhanced mappings for a plant"""
    rows = crud.get_mappings_for_plant(db, plant_id)
    return [{
        'id': r.id,
        'metadata_table': r.metadata_table,
        'field_mappings': r.field_mappings,
        'default_values': r.default_values,
        'extra_fields': r.extra_fields,
        'mapping_options': r.mapping_options,
        'created_at': r.created_at.isoformat() if r.created_at else None
    } for r in rows]

@app.get('/mappings')
def get_all_mappings(db: Session = Depends(get_db)):
    """Get all mappings across all plants"""
    rows = crud.get_all_mappings(db)
    return [{
        'id': r.id,
        'plant_id': r.plant_id,
        'metadata_table': r.metadata_table,
        'field_mappings': r.field_mappings,
        'default_values': r.default_values,
        'extra_fields': r.extra_fields,
        'mapping_options': r.mapping_options,
        'created_at': r.created_at.isoformat() if r.created_at else None
    } for r in rows]

@app.get('/mapping/{mapping_id}', response_model=schemas.MappingOut)
def get_mapping(mapping_id: int, db: Session = Depends(get_db)):
    """Get a specific mapping by ID"""
    mapping = crud.get_mapping(db, mapping_id)
    if not mapping:
        raise HTTPException(status_code=404, detail="Mapping not found")
    return mapping

@app.put('/mapping/{mapping_id}', response_model=schemas.MappingOut)
def update_mapping(mapping_id: int, mapping_update: schemas.MappingUpdate, db: Session = Depends(get_db)):
    """Update a specific mapping"""
    mapping = crud.update_mapping(db, mapping_id, mapping_update)
    if not mapping:
        raise HTTPException(status_code=404, detail="Mapping not found")
    return mapping

@app.delete('/mapping/{mapping_id}')
def delete_mapping(mapping_id: int, db: Session = Depends(get_db)):
    """Delete a specific mapping"""
    result = crud.delete_mapping(db, mapping_id)
    if result is None:
        raise HTTPException(status_code=404, detail="Mapping not found")
    elif result is False:
        raise HTTPException(status_code=400, detail="Cannot delete mapping that is being used in migration runs")
    return {"message": "Mapping deleted successfully"}

@app.post('/migrate/start')
def migrate_start(plant_id: int, mapping_id: int, limit: int | None = None, db: Session = Depends(get_db)):
    run = crud.create_migration_run(db, plant_id, mapping_id)
    task = migrate_plant_task.delay(plant_id, mapping_id, limit)
    crud.update_run_status(db, run.id, status='queued')
    return {'task_id': task.id, 'run_id': run.id}

@app.get('/migrate/runs')
def list_runs(db: Session = Depends(get_db)):
    rows = db.query(models.MigrationRun).order_by(models.MigrationRun.id.desc()).all()

    # Enhance runs with additional statistics
    enhanced_runs = []
    for run in rows:
        logs = db.query(models.MigrationLog).filter(models.MigrationLog.run_id == run.id).all()

        total = len(logs)
        succeeded = len([log for log in logs if log.status == 'success'])
        failed = len([log for log in logs if log.status == 'failed'])

        enhanced_run = {
            'id': run.id,
            'plant_id': run.plant_id,
            'mapping_id': run.mapping_id,
            'status': run.status,
            'started_at': run.started_at.isoformat() if run.started_at else None,
            'finished_at': run.finished_at.isoformat() if run.finished_at else None,
            'total': total,
            'succeeded': succeeded,
            'failed': failed
        }
        enhanced_runs.append(enhanced_run)

    return enhanced_runs

@app.get('/dashboard/stats')
def get_dashboard_stats(db: Session = Depends(get_db)):
    """Get dashboard statistics for all plants and migrations"""

    # Get plant statistics
    plants = db.query(models.Plant).all()
    total_plants = len(plants)
    active_plants = len([p for p in plants if p.active])

    # Get migration statistics
    runs = db.query(models.MigrationRun).all()
    total_migrations = len(runs)
    successful_migrations = len([r for r in runs if r.status == 'completed'])
    failed_migrations = len([r for r in runs if r.status == 'failed'])
    running_migrations = len([r for r in runs if r.status == 'running'])

    # Get document statistics
    total_documents = 0
    migrated_documents = 0
    failed_documents = 0

    for run in runs:
        logs = db.query(models.MigrationLog).filter(models.MigrationLog.run_id == run.id).all()
        total_documents += len(logs)
        migrated_documents += len([log for log in logs if log.status == 'success'])
        failed_documents += len([log for log in logs if log.status == 'failed'])

    return {
        'plants': {
            'total': total_plants,
            'active': active_plants,
            'inactive': total_plants - active_plants
        },
        'migrations': {
            'total': total_migrations,
            'successful': successful_migrations,
            'failed': failed_migrations,
            'running': running_migrations,
            'pending': total_migrations - successful_migrations - failed_migrations - running_migrations
        },
        'documents': {
            'total': total_documents,
            'migrated': migrated_documents,
            'failed': failed_documents,
            'pending': total_documents - migrated_documents - failed_documents
        }
    }

@app.get('/migrate/runs/{run_id}/logs')
def run_logs(run_id: int, db: Session = Depends(get_db)):
    logs = crud.get_run_logs(db, run_id)
    return logs

@app.post('/migrate/runs/{run_id}/retry')
def retry_log(run_id: int, log_id: int, db: Session = Depends(get_db)):
    log = crud.get_log(db, log_id)
    if not log:
        raise HTTPException(status_code=404, detail='log not found')
    task = retry_log_task.delay(log_id)
    return {'task_id': task.id}

@app.get('/migrate/runs/{run_id}/export')
def export_run_csv(run_id: int, db: Session = Depends(get_db)):
    import csv, io
    logs = crud.get_run_logs(db, run_id)
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(['doc_dkey','doc_name','status','message','attempts','paperless_id','created_at'])
    for l in logs:
        writer.writerow([l.doc_dkey, l.doc_name, l.status, l.message, l.attempts, l.paperless_id, l.created_at])
    return Response(content=output.getvalue(), media_type='text/csv')

@app.get('/plants/{plant_id}/storage')
def get_plant_storage_info(plant_id: int, db: Session = Depends(get_db)):
    """Get storage information and validation for a plant"""
    plant = crud.get_plant(db, plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    try:
        from .document_resolver import create_resolver

        resolver = create_resolver(plant)
        storage_stats = resolver.get_storage_stats()

        return {
            'plant_id': plant_id,
            'plant_name': plant.name,
            'configured_path': plant.storage_path,
            'resolved_path': resolver.plant_storage_path,
            'storage_stats': storage_stats
        }

    except Exception as e:
        return {
            'plant_id': plant_id,
            'plant_name': plant.name,
            'configured_path': plant.storage_path,
            'resolved_path': f"/Plant{plant_id}",
            'storage_stats': {
                'mounted': False,
                'error': f'Error checking storage: {str(e)}'
            }
        }

@app.post('/plants/{plant_id}/test-document-path')
def test_document_path_resolution(plant_id: int, request_data: dict, db: Session = Depends(get_db)):
    """Test document path resolution for a plant"""
    plant = crud.get_plant(db, plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    pp_docpath = request_data.get('docpath', '')
    if not pp_docpath:
        raise HTTPException(status_code=400, detail="docpath is required")

    try:
        from .document_resolver import create_resolver

        resolver = create_resolver(plant)
        docker_path, file_exists = resolver.resolve_document_path(pp_docpath)
        paperless_path = resolver.get_paperless_import_path(pp_docpath)

        return {
            'plant_id': plant_id,
            'original_path': pp_docpath,
            'resolved_path': docker_path,
            'file_exists': file_exists,
            'paperless_import_path': paperless_path,
            'storage_mounted': resolver.validate_storage_mount()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Path resolution error: {str(e)}")

@app.get('/csdocs')
def list_csdocs(plant_id: int, q: str = None, limit: int = 100, db: Session = Depends(get_db)):
    """List documents from CSDOC table with optional search"""
    plant = crud.get_plant(db, plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    try:
        from .oracle_helper import connect_plant
        from .config import settings

        # In demo mode, return mock data
        # if settings.demo_mode:
        #     mock_docs = [
        #         {'DKEY': 1001, 'DOCNAME': 'Project_Specification.pdf', 'DOCPATH': '/docs/projects/spec_001.pdf', 'RECORDKEY': 'R001'},
        #         {'DKEY': 1002, 'DOCNAME': 'Equipment_Manual.pdf', 'DOCPATH': '/docs/equipment/manual_002.pdf', 'RECORDKEY': 'R002'},
        #         {'DKEY': 1003, 'DOCNAME': 'Safety_Protocol.docx', 'DOCPATH': '/docs/safety/protocol_003.docx', 'RECORDKEY': 'R003'},
        #         {'DKEY': 1004, 'DOCNAME': 'Maintenance_Log.xlsx', 'DOCPATH': '/docs/maintenance/log_004.xlsx', 'RECORDKEY': 'R004'},
        #         {'DKEY': 1005, 'DOCNAME': 'Design_Drawing.dwg', 'DOCPATH': '/docs/drawings/design_005.dwg', 'RECORDKEY': 'R005'},
        #     ]

        #     # Apply search filter if provided
        #     if q:
        #         q_lower = q.lower()
        #         mock_docs = [doc for doc in mock_docs
        #                    if q_lower in doc['DOCNAME'].lower() or q_lower in doc['DOCPATH'].lower()]

        #     return mock_docs[:limit]

        # Real Oracle connection with cross-schema support
        conn = connect_plant(plant)
        cursor = conn.cursor()

        # Get CSDOCS schema (default to username if not specified)
        csdocs_schema = plant.csdocs_schema or plant.username

        # Build query with schema prefix and optional search
        schema_tables_query = f"""
            SELECT object_name
            FROM all_objects
            WHERE owner = '{csdocs_schema}' AND object_name like '%CSDOCS%'
        """
        cursor.execute(schema_tables_query)        
        # print(cursor.fetchone()[0])
        table_name = cursor.fetchone()[0]
        base_query = f"""
            SELECT DKEY, DOCNAME, DOCPATH, RECORDKEY
            FROM {csdocs_schema}.{table_name}
            WHERE 1=1 and ROWNUM <= {limit}
        """
        
        params = []

        if q:
            base_query += " AND (UPPER(DOCNAME) LIKE UPPER(?) OR UPPER(DOCPATH) LIKE UPPER(?))"
            search_param = f"%{q}%"
            params.extend([search_param, search_param])

        base_query += f" ORDER BY DKEY DESC"
        # base_query += f" ORDER BY DKEY DESC FETCH FIRST {limit} ROWS ONLY"
        print(base_query)
        cursor.execute(base_query, params)
        columns = [desc[0] for desc in cursor.description]
        rows = cursor.fetchall()

        # Convert to list of dictionaries and resolve document paths
        docs = []

        try:
            from .document_resolver import create_resolver
            resolver = create_resolver(plant)

            for row in rows:
                doc = dict(zip(columns, row))

                # Add resolved document path information
                if doc.get('DOCPATH'):
                    docker_path, file_exists = resolver.resolve_document_path(doc['DOCPATH'])
                    # doc['RESOLVED_PATH'] = docker_path
                    doc['FILE_EXISTS'] = file_exists
                else:
                    # doc['RESOLVED_PATH'] = None
                    doc['FILE_EXISTS'] = False

                docs.append(doc)

        except Exception as e:
            print(f"Database error 0: {e}")
            # Fallback: return docs without path resolution
            docs = [dict(zip(columns, row)) for row in rows]
            for doc in docs:
                doc['RESOLVED_PATH'] = None
                doc['FILE_EXISTS'] = None

        cursor.close()
        conn.close()

        return docs

    except Exception as e:
        print(f"Database error 1: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.get('/paperless/fields')
def get_paperless_fields(api_url: str = None, token: str = None):
    """Auto-discover available Paperless-NGX document fields, custom fields, and tags"""
    try:
        from .paperless_client import get_paperless_fields_from_api, get_tags, get_document_types, get_correspondents
        from .config import settings

        # Use provided credentials or fall back to config
        paperless_url = api_url or settings.paperless_api_url
        paperless_token = token or settings.paperless_api_token

        # Standard Paperless-NGX document fields
        standard_fields = [
            {'name': 'title', 'type': 'text', 'required': True, 'description': 'Document title'},
            {'name': 'content', 'type': 'text', 'required': False, 'description': 'Document content/description'},
            {'name': 'document_type', 'type': 'reference', 'required': False, 'description': 'Document type'},
            {'name': 'correspondent', 'type': 'reference', 'required': False, 'description': 'Correspondent'},
            {'name': 'created', 'type': 'datetime', 'required': False, 'description': 'Creation date'},
            {'name': 'created_date', 'type': 'date', 'required': False, 'description': 'Creation date'},
            {'name': 'modified', 'type': 'datetime', 'required': False, 'description': 'Modification date'},
            {'name': 'added', 'type': 'datetime', 'required': False, 'description': 'Addition date'},
            {'name': 'deleted_at', 'type': 'datetime', 'required': False, 'description': 'Deletion date'},
            {'name': 'archive_serial_number', 'type': 'number', 'required': False, 'description': 'Archive serial number'},
            {'name': 'original_file_name', 'type': 'text', 'required': False, 'description': 'Original filename'},
            {'name': "page_count", "type": "number", "required": False, "description": "Page count"},
            {'name':"mime_type", "type": "text", "required": False, "description": "Mime type"},
            {'name':"is_favorite", "type": "boolean", "required": False, "description": "Is favorite"},
            {'name':"owner", "type": "number", "required": False, "description": "Document owner"}
        ]

        # Get tags from Paperless API
        tags = []
        document_types = []
        correspondents = []
        custom_fields = []

        if paperless_url and paperless_token:
            try:
                # Fetch all reference data from Paperless
                tags = get_tags(paperless_url, paperless_token)
                document_types = get_document_types(paperless_url, paperless_token)
                correspondents = get_correspondents(paperless_url, paperless_token)
                custom_fields = get_paperless_fields_from_api(paperless_url, paperless_token)
            except Exception as e:
                print(f"Could not fetch data from Paperless: {e}")

        # If no API connection, provide demo data
        if not tags:
            tags = [
                {'id': 1, 'name': 'Important', 'color': '#ff0000', 'description': 'Important documents'},
                {'id': 2, 'name': 'Project', 'color': '#00ff00', 'description': 'Project related'},
                {'id': 3, 'name': 'Safety', 'color': '#ffff00', 'description': 'Safety documentation'},
                {'id': 4, 'name': 'Maintenance', 'color': '#0000ff', 'description': 'Maintenance records'},
                {'id': 5, 'name': 'Quality', 'color': '#ff00ff', 'description': 'Quality control'}
            ]

        if not document_types:
            document_types = [
                {'id': 1, 'name': 'Manual', 'description': 'Equipment manuals'},
                {'id': 2, 'name': 'Procedure', 'description': 'Standard procedures'},
                {'id': 3, 'name': 'Report', 'description': 'Technical reports'},
                {'id': 4, 'name': 'Drawing', 'description': 'Technical drawings'},
                {'id': 5, 'name': 'Certificate', 'description': 'Certificates and compliance'}
            ]

        if not correspondents:
            correspondents = [
                {'id': 1, 'name': 'Engineering', 'description': 'Engineering department'},
                {'id': 2, 'name': 'Maintenance', 'description': 'Maintenance department'},
                {'id': 3, 'name': 'Quality', 'description': 'Quality assurance'},
                {'id': 4, 'name': 'Safety', 'description': 'Safety department'},
                {'id': 5, 'name': 'External', 'description': 'External vendors'}
            ]

        # If no API connection, provide default custom field slots
        if not custom_fields:
            for i in range(1, 11):  # Support up to 10 custom fields
                custom_fields.append({
                    'name': f'custom_field_{i}',
                    'type': 'text',
                    'required': False,
                    'description': f'Custom field {i} (available for mapping)',
                    'is_custom': True,
                    'exists': False  # Will be created if mapped
                })

        return {
            'standard_fields': standard_fields,
            'custom_fields': custom_fields,
            'tags': tags,
            'document_types': document_types,
            'correspondents': correspondents,
            'special_mappings': [
                {'name': 'tag:', 'description': 'Map to document tags (use tag ID)'},
                {'name': 'default:', 'description': 'Set default value (use default:value)'},
                {'name': 'ignore', 'description': 'Ignore this field during migration'}
            ],
            'mapping_options': {
                'allow_extra_fields': True,
                'allow_default_values': True,
                'tag_mapping_by_id': True
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching Paperless fields: {str(e)}")

@app.post('/paperless/custom-fields')
def create_paperless_custom_field(field_data: dict, api_url: str = None, token: str = None):
    """Create a new custom field in Paperless-NGX"""
    try:
        from .paperless_client import create_custom_field
        from .config import settings

        # Use provided credentials or fall back to config
        paperless_url = api_url or settings.paperless_api_url
        paperless_token = token or settings.paperless_api_token

        if not paperless_url or not paperless_token:
            raise HTTPException(status_code=400, detail="Paperless API URL and token are required")
        result = create_custom_field(paperless_url, paperless_token, field_data)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating custom field: {str(e)}")

@app.get('/paperless/document-types')
def get_paperless_document_types(api_url: str = None, token: str = None):
    """Get available document types from Paperless-NGX"""
    try:
        from .paperless_client import get_document_types
        from .config import settings

        paperless_url = api_url or settings.paperless_api_url
        paperless_token = token or settings.paperless_api_token

        if not paperless_url or not paperless_token:
            return []  # Return empty if no API configured

        return get_document_types(paperless_url, paperless_token)

    except Exception as e:
        return []  # Return empty on error

@app.get('/paperless/correspondents')
def get_paperless_correspondents(api_url: str = None, token: str = None):
    """Get available correspondents from Paperless-NGX"""
    try:
        from .paperless_client import get_correspondents
        from .config import settings

        paperless_url = api_url or settings.paperless_api_url
        paperless_token = token or settings.paperless_api_token

        if not paperless_url or not paperless_token:
            return []

        return get_correspondents(paperless_url, paperless_token)

    except Exception as e:
        return []

@app.get('/paperless/tags')
def get_paperless_tags(api_url: str = None, token: str = None):
    """Get available tags from Paperless-NGX"""
    try:
        from .paperless_client import get_tags
        from .config import settings

        paperless_url = api_url or settings.paperless_api_url
        paperless_token = token or settings.paperless_api_token

        if not paperless_url or not paperless_token:
            return []

        return get_tags(paperless_url, paperless_token)

    except Exception as e:
        return []

@app.post('/paperless/test')
def test_paperless_connection(config: dict):
    """Test connection to Paperless-NGX API"""
    try:
        from .paperless_client import test_paperless_api
        api_url = config.get('api_url')
        token = config.get('token')

        if not api_url or not token:
            raise HTTPException(status_code=400, detail="API URL and token are required")

        result = test_paperless_api(api_url, token)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Connection test failed: {str(e)}")
