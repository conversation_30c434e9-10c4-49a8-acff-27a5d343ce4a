#!/usr/bin/env python3
"""
Migration script to add enhanced mapping columns to the database.
Supports:
1. Tags as IDs
2. Extra Paperless fields without PP equivalents
3. Default values for any field
"""

import sqlite3
import os
import sys

def migrate_database():
    """Add enhanced mapping columns to the database"""
    
    # Database path
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'app.db')
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Adding enhanced mapping columns...")
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(mappings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add new columns if they don't exist
        if 'default_values' not in columns:
            cursor.execute("ALTER TABLE mappings ADD COLUMN default_values TEXT")
            print("✅ Added default_values column")
        else:
            print("ℹ️  default_values column already exists")
            
        if 'extra_fields' not in columns:
            cursor.execute("ALTER TABLE mappings ADD COLUMN extra_fields TEXT")
            print("✅ Added extra_fields column")
        else:
            print("ℹ️  extra_fields column already exists")
            
        if 'mapping_options' not in columns:
            cursor.execute("ALTER TABLE mappings ADD COLUMN mapping_options TEXT")
            print("✅ Added mapping_options column")
        else:
            print("ℹ️  mapping_options column already exists")
        
        # Commit changes
        conn.commit()
        print("✅ Enhanced mapping migration completed successfully!")
        
        # Show current table structure
        cursor.execute("PRAGMA table_info(mappings)")
        columns = cursor.fetchall()
        print("\n📋 Current mappings table structure:")
        for column in columns:
            print(f"   - {column[1]} ({column[2]})")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
    finally:
        if conn:
            conn.close()
    
    return True

if __name__ == "__main__":
    print("🚀 Enhanced Mapping Migration")
    print("=" * 50)
    
    success = migrate_database()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("\nNew features available:")
        print("• Tags mapped by ID (tag:123)")
        print("• Extra TechDoc fields without PP equivalents")
        print("• Default values for any field or custom field")
        print("• Enhanced mapping configuration options")
    else:
        print("\n💥 Migration failed!")
        sys.exit(1)
