import React from 'react'

const Badge = ({ children, variant = 'info', className = '' }) => {
  const variantClasses = {
    success: 'badge-success',
    warning: 'badge-warning',
    danger: 'badge-danger',
    info: 'badge-info',
  }
  
  const classes = [
    'badge',
    variantClasses[variant],
    className
  ].filter(Boolean).join(' ')
  
  return (
    <span className={classes}>
      {children}
    </span>
  )
}

export default Badge
