import React, { useEffect, useState } from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'
import { Card, Button, Table, LoadingSpinner, Badge, Input } from '../components/ui'
import { GitBranch, Database, Save, Zap, AlertCircle, CheckCircle, Plus, X, Eye } from 'lucide-react'

// Simple similarity function for auto-mapping suggestions
function calculateSimilarity(a, b) {
  if (!a || !b) return 0
  a = a.toLowerCase()
  b = b.toLowerCase()
  if (a === b) return 1
  return (a.split('').filter(c => b.includes(c)).length) / Math.max(a.length, b.length)
}

export default function Mapping() {
  const selectedPlant = useStore(s => s.selectedPlant)
  const [tables, setTables] = useState([])
  const [schemas, setSchemas] = useState([])
  const [selectedSchema, setSelectedSchema] = useState('')
  const [filteredTables, setFilteredTables] = useState([])
  const [selectedTable, setSelectedTable] = useState('')
  const [selectedTableSchema, setSelectedTableSchema] = useState('')
  const [columns, setColumns] = useState([])
  const [paperlessFields, setPaperlessFields] = useState({})
  const [mapping, setMapping] = useState({})
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [loadingColumns, setLoadingColumns] = useState(false)
  const [saveResult, setSaveResult] = useState(null)
  const [showCustomFieldModal, setShowCustomFieldModal] = useState(false)
  const [newCustomField, setNewCustomField] = useState({ name: '', description: '', type: 'string' })
  const [showExtraFieldModal, setShowExtraFieldModal] = useState(false)
  const [extraFields, setExtraFields] = useState([])
  const [defaultValues, setDefaultValues] = useState({})
  const [showPreviewModal, setShowPreviewModal] = useState(false)
  const [previewData, setPreviewData] = useState(null)
  const [loadingPreview, setLoadingPreview] = useState(false)

  useEffect(() => {
    if (selectedPlant) {
      loadTables()
    }
    fetchPaperlessFields()
  }, [selectedPlant])

  async function loadTables() {
    if (!selectedPlant) return

    setLoading(true)
    try {
      const response = await api.get(`/metadata/tables/${selectedPlant.id}`)
      console.log("Metadata Tables : ",response.data)
      setTables(response.data)

      // Extract unique schemas
      const uniqueSchemas = [...new Set(response.data.map(table => table.schema))].sort()
      setSchemas(uniqueSchemas)

      // Reset selections when tables are reloaded
      setSelectedSchema('')
      setSelectedTable('')
      setSelectedTableSchema('')
      setFilteredTables([])
    } catch (e) {
      console.error(e)
    } finally {
      setLoading(false)
    }
  }

  function handleSchemaChange(schema) {
    setSelectedSchema(schema)
    setSelectedTable('')
    setSelectedTableSchema('')

    // Filter tables by selected schema
    const tablesInSchema = tables.filter(table => table.schema === schema)
    setFilteredTables(tablesInSchema)

    // Clear columns when schema changes
    setColumns([])
    setMapping({})
    setSaveResult(null)
  }

  async function fetchColumns(tableName,tableSchema) {
    setSelectedTableSchema(tableSchema)
    setSelectedTable(tableName)
    setLoadingColumns(true)
    setSaveResult(null)

    try {
      const response = await api.get(`/metadata/columns/${selectedPlant.id}/${tableSchema}/${tableName}`)
      setColumns(response.data)

      // Auto-suggest mappings based on similarity
      const suggestions = {}
      const allFields = [
        ...paperlessFields.standard_fields?.map(f => f.name) || [],
        ...paperlessFields.custom_fields?.map(f => f.name) || []
      ]

      for (const column of response.data) {
        let bestMatch = null
        let bestScore = 0

        for (const field of allFields) {
          const score = calculateSimilarity(column.column_name, field)
          if (score > bestScore) {
            bestScore = score
            bestMatch = field
          }
        }

        // Only suggest if similarity is above threshold
        if (bestScore > 0.4) {
          suggestions[column.column_name] = bestMatch
        }
      }

      setMapping(suggestions)
    } catch (e) {
      console.error(e)
    } finally {
      setLoadingColumns(false)
    }
  }

  async function fetchPaperlessFields() {
    try {
      const response = await api.get('/paperless/fields')
      setPaperlessFields(response.data)
    } catch (e) {
      console.warn('TechDoc fields not available')
      // Fallback to basic fields
      setPaperlessFields({
        standard_fields: [
          { name: 'title', type: 'text', required: true, description: 'Document title' },
          { name: 'content', type: 'text', required: false, description: 'Document content' },
          { name: 'tags', type: 'array', required: false, description: 'Document tags' }
        ],
        custom_fields: [],
        special_mappings: [
          { name: 'tag:', description: 'Map to document tags' },
          { name: 'ignore', description: 'Ignore this field' }
        ]
      })
    }
  }

  async function createCustomField() {
    if (!newCustomField.name.trim()) return

    try {
      const response = await api.post('/paperless/custom-fields', {
        name: newCustomField.name,
        description: newCustomField.description,
        data_type: newCustomField.type
      })

      if (response.data.success) {
        // Refresh fields
        await fetchPaperlessFields()
        setShowCustomFieldModal(false)
        setNewCustomField({ name: '', description: '', type: 'string' })
        setSaveResult({
          success: true,
          message: 'Custom field created successfully!'
        })
      } else {
        setSaveResult({
          success: false,
          message: response.data.error || 'Failed to create custom field'
        })
      }
    } catch (e) {
      setSaveResult({
        success: false,
        message: 'Failed to create custom field. Please try again.'
      })
    }
  }

  function updateMapping(columnName, fieldValue) {
    setMapping({ ...mapping, [columnName]: fieldValue })
    setSaveResult(null)
  }

  function updateDefaultValue(fieldName, defaultValue) {
    setDefaultValues({ ...defaultValues, [fieldName]: defaultValue })
    setSaveResult(null)
  }

  function addExtraField(fieldName, fieldValue) {
    const newExtraField = { id: Date.now(), fieldName, fieldValue }
    setExtraFields([...extraFields, newExtraField])
    setSaveResult(null)
  }

  function removeExtraField(fieldId) {
    setExtraFields(extraFields.filter(f => f.id !== fieldId))
    setSaveResult(null)
  }

  function handleAddExtraField() {
    setShowExtraFieldModal(true)
  }

  async function handlePreviewTable() {
    if (!selectedPlant || !selectedTable || !selectedTableSchema) return

    setLoadingPreview(true)
    setShowPreviewModal(true)

    try {
      const response = await api.get(`/metadata/preview/${selectedPlant.id}/${selectedTableSchema}/${selectedTable}?limit=100`)
      setPreviewData(response.data)
    } catch (e) {
      console.error('Failed to load preview data:', e)
      setPreviewData({
        error: 'Failed to load preview data',
        message: e.response?.data?.detail || e.message
      })
    } finally {
      setLoadingPreview(false)
    }
  }

  async function saveMapping() {
    if (!selectedPlant || !selectedTable) return

    setSaving(true)
    setSaveResult(null)

    try {
      // Prepare enhanced mapping data
      const enhancedMapping = {
        field_mappings: mapping,
        default_values: defaultValues,
        extra_fields: extraFields.reduce((acc, field) => {
          acc[field.fieldName] = field.fieldValue
          return acc
        }, {}),
        mapping_options: {
          allow_extra_fields: true,
          allow_default_values: true,
          tag_mapping_by_id: true
        }
      }

      await api.post('/mappings', {
        plant_id: selectedPlant.id,
        metadata_table: selectedTable,
        field_mappings: enhancedMapping
      })
      setSaveResult({
        success: true,
        message: 'Enhanced mapping saved successfully!'
      })
    } catch (e) {
      console.error(e)
      setSaveResult({
        success: false,
        message: 'Failed to save mapping. Please try again.'
      })
    } finally {
      setSaving(false)
    }
  }

  const mappedCount = Object.values(mapping).filter(Boolean).length
  const totalColumns = columns.length

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
          <GitBranch className="w-5 h-5 text-primary-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Field Mapping</h1>
          <p className="text-gray-600">Map database columns to TechDoc document fields</p>
        </div>
      </div>

      {!selectedPlant ? (
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Plant Selected</h3>
              <p className="text-gray-600 mb-4">
                Please select a plant from the Plants page to configure field mappings.
              </p>
              <Button variant="primary" onClick={() => window.location.href = '/plants'}>
                Go to Plants
              </Button>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <>
          {/* Schema and Table Selection */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center space-x-2">
                <Database className="w-5 h-5" />
                <span>Database Schema & Tables</span>
              </Card.Title>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="md" />
                  <span className="ml-3 text-gray-600">Loading tables...</span>
                </div>
              ) : tables.length === 0 ? (
                <div className="text-center py-8">
                  <AlertCircle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">No tables found in the selected plant.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Schema Selection Dropdown */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Database className="w-4 h-4 inline mr-1" />
                      Select Schema ({schemas.length} available)
                    </label>
                    <select
                      value={selectedSchema}
                      onChange={(e) => handleSchemaChange(e.target.value)}
                      className="input w-full max-w-md"
                      disabled={schemas.length === 0}
                    >
                      <option value="">-- Select a schema --</option>
                      {schemas.map((schema) => (
                        <option key={schema} value={schema}>
                          {schema}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Table Selection Dropdown */}
                  {selectedSchema && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <GitBranch className="w-4 h-4 inline mr-1" />
                        Select Table ({filteredTables.length} in {selectedSchema})
                      </label>
                      <select
                        value={selectedTable}
                        onChange={(e) => {
                          const tableName = e.target.value
                          if (tableName) {
                            fetchColumns(tableName, selectedSchema)
                          } else {
                            setSelectedTable('')
                            setSelectedTableSchema('')
                            setColumns([])
                            setMapping({})
                          }
                        }}
                        className="input w-full max-w-md"
                        disabled={filteredTables.length === 0}
                      >
                        <option value="">-- Select a table --</option>
                        {filteredTables.map((table) => (
                          <option key={table.table_name} value={table.table_name}>
                            {table.table_name}
                            {table.description && ` - ${table.description}`}
                          </option>
                        ))}
                      </select>
                      {filteredTables.length === 0 && (
                        <p className="text-sm text-gray-500 mt-1">
                          No tables found in schema "{selectedSchema}"
                        </p>
                      )}
                    </div>
                  )}

                  {/* Selected Table Info */}
                  {selectedTable && selectedSchema && (
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Database className="w-4 h-4 text-blue-600" />
                        <span className="font-medium text-blue-800">
                          Selected: {selectedSchema}.{selectedTable}
                        </span>
                      </div>
                      {filteredTables.find(t => t.table_name === selectedTable)?.description && (
                        <p className="text-sm text-blue-700 mt-1">
                          {filteredTables.find(t => t.table_name === selectedTable)?.description}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Column Mapping */}
          {selectedTable && (
            <Card>
              <Card.Header>
                <div className="flex items-center justify-between">
                  <Card.Title className="flex items-center space-x-2">
                    <GitBranch className="w-5 h-5" />
                    <span>Column Mapping for: {selectedTable}</span>
                    <Button
                      onClick={handlePreviewTable}
                      variant="outline"
                      size="xs"
                      className="flex items-center space-x-1 ml-2"
                      title="Preview table data"
                    >
                      <Eye className="w-3 h-3" />
                      <span className="hidden sm:inline">Preview</span>
                    </Button>
                  </Card.Title>
                  <div className="flex items-center space-x-3">
                    <Badge variant={mappedCount === totalColumns ? "success" : "warning"}>
                      {mappedCount}/{totalColumns} mapped
                    </Badge>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setShowCustomFieldModal(true)}
                      className="flex items-center space-x-1"
                    >
                      <Plus className="w-3 h-3" />
                      <span>Add Custom Field</span>
                    </Button>
                    {mappedCount > 0 && (
                      <Button
                        onClick={saveMapping}
                        loading={saving}
                        disabled={saving}
                        className="flex items-center space-x-2"
                      >
                        <Save className="w-4 h-4" />
                        <span>Save Mapping</span>
                      </Button>
                    )}
                  </div>
                </div>
              </Card.Header>
              <Card.Body className="p-0">
                {loadingColumns ? (
                  <div className="flex items-center justify-center py-12">
                    <LoadingSpinner size="lg" />
                    <span className="ml-3 text-gray-600">Loading columns...</span>
                  </div>
                ) : columns.length === 0 ? (
                  <div className="text-center py-12">
                    <AlertCircle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">No columns found in the selected table.</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <Table.Header>
                        <Table.Row>
                          <Table.HeaderCell>Column Name</Table.HeaderCell>
                          <Table.HeaderCell>Data Type</Table.HeaderCell>
                          <Table.HeaderCell>TechDoc Field</Table.HeaderCell>
                          <Table.HeaderCell>Default Value</Table.HeaderCell>
                          <Table.HeaderCell>Status</Table.HeaderCell>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {columns.map((column) => (
                          <Table.Row key={column.column_name}>
                            <Table.Cell>
                              <div className="flex flex-col space-y-1">
                                <div className="flex items-center space-x-2">
                                  <Database className="w-4 h-4 text-gray-400" />
                                  <span className="font-mono font-medium">{column.column_name}</span>
                                </div>
                                {column.description && (
                                  <span className="text-xs text-gray-500">{column.description}</span>
                                )}
                              </div>
                            </Table.Cell>
                            <Table.Cell>
                              <Badge variant="info">{column.data_type}</Badge>
                              {column.nullable === 'N' && (
                                <Badge variant="warning" className="ml-1">Required</Badge>
                              )}
                            </Table.Cell>
                            <Table.Cell>
                              <select
                                value={mapping[column.column_name] || ''}
                                onChange={(e) => updateMapping(column.column_name, e.target.value)}
                                className="input w-full max-w-xs"
                              >
                                <option value="">-- Select field --</option>

                                {/* Standard TechDoc fields */}
                                <optgroup label="Standard Fields">
                                  {paperlessFields.standard_fields?.map((field) => (
                                    <option key={field.name} value={field.name}>
                                      {field.name} {field.required ? '(required)' : ''}
                                    </option>
                                  ))}
                                </optgroup>

                                {/* Custom fields */}
                                {paperlessFields.custom_fields?.length > 0 && (
                                  <optgroup label="Custom Fields">
                                    {paperlessFields.custom_fields.map((field) => (
                                      <option key={field.name} value={field.name}>
                                        {field.name} {field.exists ? '(exists)' : '(will create)'}
                                      </option>
                                    ))}
                                  </optgroup>
                                )}

                                {/* Tags */}
                                {paperlessFields.tags?.length > 0 && (
                                  <optgroup label="Tags (by ID)">
                                    {paperlessFields.tags.map((tag) => (
                                      <option key={tag.id} value={`tag:${tag.id}`}>
                                        Tag: {tag.name} (ID: {tag.id})
                                      </option>
                                    ))}
                                  </optgroup>
                                )}

                                {/* Document Types */}
                                {paperlessFields.document_types?.length > 0 && (
                                  <optgroup label="Document Types">
                                    {paperlessFields.document_types.map((type) => (
                                      <option key={type.id} value={`document_type:${type.id}`}>
                                        Type: {type.name} (ID: {type.id})
                                      </option>
                                    ))}
                                  </optgroup>
                                )}

                                {/* Correspondents */}
                                {paperlessFields.correspondents?.length > 0 && (
                                  <optgroup label="Correspondents">
                                    {paperlessFields.correspondents.map((correspondent) => (
                                      <option key={correspondent.id} value={`correspondent:${correspondent.id}`}>
                                        Correspondent: {correspondent.name} (ID: {correspondent.id})
                                      </option>
                                    ))}
                                  </optgroup>
                                )}

                                {/* Special mappings */}
                                {paperlessFields.special_mappings?.length > 0 && (
                                  <optgroup label="Special Mappings">
                                    {paperlessFields.special_mappings.map((mapping) => (
                                      <option key={mapping.name} value={mapping.name}>
                                        {mapping.description}
                                      </option>
                                    ))}
                                  </optgroup>
                                )}
                              </select>
                            </Table.Cell>
                            <Table.Cell>
                              <Input
                                type="text"
                                placeholder="Optional default value"
                                value={defaultValues[mapping[column.column_name]] || ''}
                                onChange={(e) => updateDefaultValue(mapping[column.column_name], e.target.value)}
                                disabled={!mapping[column.column_name]}
                                className="w-full max-w-xs text-sm"
                              />
                              {mapping[column.column_name]?.startsWith('tag:') && (
                                <div className="text-xs text-blue-600 mt-1">
                                  Tag ID mapping
                                </div>
                              )}
                            </Table.Cell>
                            <Table.Cell>
                              {mapping[column.column_name] ? (
                                <div className="flex items-center space-x-1">
                                  <CheckCircle className="w-4 h-4 text-success-600" />
                                  <span className="text-success-700 text-sm">Mapped</span>
                                </div>
                              ) : (
                                <div className="flex items-center space-x-1">
                                  <AlertCircle className="w-4 h-4 text-gray-400" />
                                  <span className="text-gray-500 text-sm">Unmapped</span>
                                </div>
                              )}
                            </Table.Cell>
                          </Table.Row>
                        ))}
                      </Table.Body>
                    </Table>
                  </div>
                )}
              </Card.Body>
            </Card>
          )}

          {/* Extra Fields Section */}
          {selectedTable && (
            <Card>
              <Card.Header>
                <div className="flex items-center justify-between">
                  <Card.Title className="flex items-center space-x-2">
                    <Plus className="w-5 h-5" />
                    <span>Extra TechDoc Fields</span>
                  </Card.Title>
                  <Button
                    onClick={handleAddExtraField}
                    variant="outline"
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Field</span>
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  Add TechDoc fields that don't have equivalents in the PP metadata table
                </p>
              </Card.Header>
              <Card.Body>
                {extraFields.length === 0 ? (
                  <div className="text-center py-8">
                    <Plus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No extra fields</h3>
                    <p className="text-gray-600">Add TechDoc fields that will have default values or be populated during migration.</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {extraFields.map((field) => (
                      <div key={field.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{field.fieldName}</div>
                          <div className="text-sm text-gray-600">Value: {field.fieldValue}</div>
                        </div>
                        <Button
                          onClick={() => removeExtraField(field.id)}
                          variant="danger"
                          size="xs"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </Card.Body>
            </Card>
          )}

          {/* Save Result */}
          {saveResult && (
            <Card>
              <Card.Body>
                <div className={`p-4 rounded-lg border ${
                  saveResult.success
                    ? 'bg-success-50 border-success-200'
                    : 'bg-danger-50 border-danger-200'
                }`}>
                  <div className="flex items-center space-x-3">
                    {saveResult.success ? (
                      <CheckCircle className="w-5 h-5 text-success-600" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-danger-600" />
                    )}
                    <span className={`font-medium ${
                      saveResult.success ? 'text-success-800' : 'text-danger-800'
                    }`}>
                      {saveResult.message}
                    </span>
                  </div>
                </div>
              </Card.Body>
            </Card>
          )}

          {/* Auto-mapping Info */}
          {selectedTable && columns.length > 0 && (
            <Card>
              <Card.Body>
                <div className="flex items-start space-x-3 p-4 bg-primary-50 rounded-lg border border-primary-200">
                  <Zap className="w-5 h-5 text-primary-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-primary-800">Auto-mapping Enabled</h4>
                    <p className="text-sm text-primary-700 mt-1">
                      Column names are automatically matched with TechDoc fields based on similarity.
                      You can adjust these suggestions or add additional mappings manually.
                    </p>
                  </div>
                </div>
              </Card.Body>
            </Card>
          )}
        </>
      )}

      {/* Custom Field Creation Modal */}
      {showCustomFieldModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Create Custom Field</h3>
                <button
                  onClick={() => setShowCustomFieldModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Field Name
                  </label>
                  <Input
                    value={newCustomField.name}
                    onChange={(e) => setNewCustomField({ ...newCustomField, name: e.target.value })}
                    placeholder="e.g., project_code"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <Input
                    value={newCustomField.description}
                    onChange={(e) => setNewCustomField({ ...newCustomField, description: e.target.value })}
                    placeholder="Brief description of this field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Field Type
                  </label>
                  <select
                    value={newCustomField.type}
                    onChange={(e) => setNewCustomField({ ...newCustomField, type: e.target.value })}
                    className="input w-full"
                  >
                    <option value="string">Text</option>
                    <option value="number">Number</option>
                    <option value="date">Date</option>
                    <option value="boolean">Boolean</option>
                    <option value="select">Options</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="secondary"
                  onClick={() => setShowCustomFieldModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={createCustomField}
                  disabled={!newCustomField.name.trim()}
                >
                  Create Field
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Extra Field Modal */}
      {showExtraFieldModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Extra TechDoc Field</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    TechDoc Field
                  </label>
                  <select
                    className="input w-full"
                    onChange={(e) => {
                      const [fieldName, fieldValue] = e.target.value.split('|')
                      setNewCustomField({ ...newCustomField, name: fieldName, value: fieldValue })
                    }}
                  >
                    <option value="">-- Select TechDoc field --</option>

                    {/* Standard fields */}
                    <optgroup label="Standard Fields">
                      {paperlessFields.standard_fields?.map((field) => (
                        <option key={field.name} value={`${field.name}|`}>
                          {field.name}
                        </option>
                      ))}
                    </optgroup>

                    {/* Tags */}
                    {paperlessFields.tags?.length > 0 && (
                      <optgroup label="Tags">
                        {paperlessFields.tags.map((tag) => (
                          <option key={tag.id} value={`tags|${tag.id}`}>
                            Tag: {tag.name} (ID: {tag.id})
                          </option>
                        ))}
                      </optgroup>
                    )}

                    {/* Custom fields */}
                    {paperlessFields.custom_fields?.length > 0 && (
                      <optgroup label="Custom Fields">
                        {paperlessFields.custom_fields.map((field) => (
                          <option key={field.name} value={`${field.name}|`}>
                            {field.name}
                          </option>
                        ))}
                      </optgroup>
                    )}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Default Value
                  </label>
                  <Input
                    type="text"
                    placeholder="Enter default value"
                    value={newCustomField.value || ''}
                    onChange={(e) => setNewCustomField({ ...newCustomField, value: e.target.value })}
                    className="w-full"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  onClick={() => {
                    setShowExtraFieldModal(false)
                    setNewCustomField({ name: '', value: '' })
                  }}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    if (newCustomField.name) {
                      addExtraField(newCustomField.name, newCustomField.value || '')
                      setShowExtraFieldModal(false)
                      setNewCustomField({ name: '', value: '' })
                    }
                  }}
                  disabled={!newCustomField.name}
                >
                  Add Field
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreviewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                  <Eye className="w-5 h-5" />
                  <span>Table Preview: {selectedTable}</span>
                </h3>
                <Button
                  onClick={() => {
                    setShowPreviewModal(false)
                    setPreviewData(null)
                  }}
                  variant="outline"
                  size="sm"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {loadingPreview ? (
                <div className="flex items-center justify-center py-12">
                  <LoadingSpinner className="w-8 h-8" />
                  <span className="ml-3 text-gray-600">Loading preview data...</span>
                </div>
              ) : previewData?.error ? (
                <div className="text-center py-12">
                  <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Preview Error</h3>
                  <p className="text-gray-600 mb-4">{previewData.message}</p>
                </div>
              ) : previewData ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      Showing {previewData.total_rows} rows
                      {previewData.demo_mode && (
                        <Badge variant="info" className="ml-2">Demo Data</Badge>
                      )}
                    </div>
                    {previewData.schema && (
                      <div className="text-sm text-gray-600">
                        Schema: <span className="font-medium">{previewData.schema}</span>
                      </div>
                    )}
                  </div>

                  <div className="overflow-auto max-h-96 border rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          {previewData.columns?.map((column) => (
                            <th
                              key={column}
                              className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0"
                            >
                              {column}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {previewData.data?.map((row, index) => (
                          <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            {previewData.columns?.map((column) => (
                              <td
                                key={column}
                                className="px-3 py-2 text-sm text-gray-900 border-r border-gray-200 last:border-r-0 max-w-xs truncate"
                                title={row[column]}
                              >
                                {row[column] || <span className="text-gray-400 italic">null</span>}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {previewData.total_rows === 0 && (
                    <div className="text-center py-8">
                      <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Found</h3>
                      <p className="text-gray-600">The table appears to be empty.</p>
                    </div>
                  )}
                </div>
              ) : null}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
