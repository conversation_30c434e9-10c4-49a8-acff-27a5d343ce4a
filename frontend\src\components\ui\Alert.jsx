import React from 'react'
import { <PERSON>ert<PERSON>ircle, CheckCircle, Info, AlertTriangle, X } from 'lucide-react'

const Alert = ({ 
  children, 
  variant = 'info', 
  dismissible = false, 
  onDismiss,
  className = '' 
}) => {
  const variants = {
    success: {
      container: 'bg-success-50 border-success-200 text-success-800',
      icon: CheckCircle,
      iconColor: 'text-success-600'
    },
    danger: {
      container: 'bg-danger-50 border-danger-200 text-danger-800',
      icon: AlertCircle,
      iconColor: 'text-danger-600'
    },
    warning: {
      container: 'bg-warning-50 border-warning-200 text-warning-800',
      icon: AlertTriangle,
      iconColor: 'text-warning-600'
    },
    info: {
      container: 'bg-primary-50 border-primary-200 text-primary-800',
      icon: Info,
      iconColor: 'text-primary-600'
    }
  }

  const config = variants[variant]
  const Icon = config.icon

  return (
    <div className={`p-4 border rounded-lg ${config.container} ${className}`}>
      <div className="flex items-start space-x-3">
        <Icon className={`w-5 h-5 ${config.iconColor} mt-0.5 flex-shrink-0`} />
        <div className="flex-1 min-w-0">
          {children}
        </div>
        {dismissible && onDismiss && (
          <button
            onClick={onDismiss}
            className={`${config.iconColor} hover:opacity-75 flex-shrink-0`}
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  )
}

export default Alert
