import React, { useEffect, useState } from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'
import { Card, But<PERSON>, Table, LoadingSpinner, Badge, Input } from '../components/ui'
import { Database, Edit, Trash2, Plus, Eye, Save, X, CheckCircle, AlertCircle } from 'lucide-react'

export default function MappingManager() {
  const selectedPlant = useStore(s => s.selectedPlant)
  const [mappings, setMappings] = useState([])
  const [loading, setLoading] = useState(false)
  const [editingMapping, setEditingMapping] = useState(null)
  const [viewingMapping, setViewingMapping] = useState(null)
  const [deleteConfirm, setDeleteConfirm] = useState(null)

  useEffect(() => {
    if (selectedPlant) {
      loadMappings()
    }
  }, [selectedPlant])

  async function loadMappings() {
    if (!selectedPlant) return
    
    setLoading(true)
    try {
      const response = await api.get(`/mappings/${selectedPlant.id}`)
      setMappings(response.data)
    } catch (e) {
      console.error('Failed to load mappings:', e)
    } finally {
      setLoading(false)
    }
  }

  async function deleteMapping(mappingId) {
    try {
      await api.delete(`/mapping/${mappingId}`)
      setMappings(mappings.filter(m => m.id !== mappingId))
      setDeleteConfirm(null)
    } catch (e) {
      console.error('Failed to delete mapping:', e)
      alert('Failed to delete mapping. It may be in use by migration runs.')
    }
  }

  function formatJson(jsonString) {
    if (!jsonString) return 'None'
    try {
      const parsed = JSON.parse(jsonString)
      return Object.keys(parsed).length === 0 ? 'None' : JSON.stringify(parsed, null, 2)
    } catch {
      return jsonString
    }
  }

  function parseJson(jsonString) {
    if (!jsonString) return {}
    try {
      return JSON.parse(jsonString)
    } catch {
      return {}
    }
  }

  if (!selectedPlant) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Database className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Plant Selected</h3>
              <p className="text-gray-600">Please select a plant to manage its field mappings.</p>
            </div>
          </Card.Body>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mapping Manager</h1>
          <p className="text-gray-600 mt-1">
            Manage field mappings for <span className="font-semibold">{selectedPlant.name}</span>
          </p>
        </div>
        <Button
          onClick={() => window.location.href = '/mapping'}
          className="flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Create New Mapping</span>
        </Button>
      </div>

      {loading ? (
        <Card>
          <Card.Body>
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner className="w-8 h-8" />
              <span className="ml-3 text-gray-600">Loading mappings...</span>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <Card>
          <Card.Header>
            <Card.Title className="flex items-center space-x-2">
              <Database className="w-5 h-5" />
              <span>Field Mappings ({mappings.length})</span>
            </Card.Title>
          </Card.Header>
          <Card.Body>
            {mappings.length === 0 ? (
              <div className="text-center py-12">
                <Database className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Mappings Found</h3>
                <p className="text-gray-600 mb-4">Create your first field mapping to get started.</p>
                <Button
                  onClick={() => window.location.href = '/mapping'}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Create Mapping</span>
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <Table.Header>
                    <Table.Row>
                      <Table.HeaderCell>Metadata Table</Table.HeaderCell>
                      <Table.HeaderCell>Field Mappings</Table.HeaderCell>
                      <Table.HeaderCell>Enhanced Features</Table.HeaderCell>
                      <Table.HeaderCell>Created</Table.HeaderCell>
                      <Table.HeaderCell>Actions</Table.HeaderCell>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {mappings.map((mapping) => {
                      const fieldMappings = parseJson(mapping.field_mappings)
                      const defaultValues = parseJson(mapping.default_values)
                      const extraFields = parseJson(mapping.extra_fields)
                      const hasDefaults = Object.keys(defaultValues).length > 0
                      const hasExtras = Object.keys(extraFields).length > 0
                      const hasTagMapping = Object.values(fieldMappings).some(v => v?.startsWith?.('tag:'))

                      return (
                        <Table.Row key={mapping.id}>
                          <Table.Cell>
                            <div className="font-medium text-gray-900">{mapping.metadata_table}</div>
                            <div className="text-sm text-gray-500">ID: {mapping.id}</div>
                          </Table.Cell>
                          <Table.Cell>
                            <div className="text-sm">
                              <div className="font-medium">{Object.keys(fieldMappings).length} mappings</div>
                              <div className="text-gray-500 truncate max-w-xs">
                                {Object.entries(fieldMappings).slice(0, 2).map(([k, v]) => `${k}→${v}`).join(', ')}
                                {Object.keys(fieldMappings).length > 2 && '...'}
                              </div>
                            </div>
                          </Table.Cell>
                          <Table.Cell>
                            <div className="flex flex-wrap gap-1">
                              {hasTagMapping && (
                                <Badge variant="info" className="text-xs">Tag IDs</Badge>
                              )}
                              {hasDefaults && (
                                <Badge variant="success" className="text-xs">Defaults</Badge>
                              )}
                              {hasExtras && (
                                <Badge variant="warning" className="text-xs">Extra Fields</Badge>
                              )}
                              {!hasTagMapping && !hasDefaults && !hasExtras && (
                                <Badge variant="secondary" className="text-xs">Basic</Badge>
                              )}
                            </div>
                          </Table.Cell>
                          <Table.Cell>
                            <div className="text-sm text-gray-500">
                              {mapping.created_at ? new Date(mapping.created_at).toLocaleDateString() : 'Unknown'}
                            </div>
                          </Table.Cell>
                          <Table.Cell>
                            <div className="flex items-center space-x-2">
                              <Button
                                onClick={() => setViewingMapping(mapping)}
                                variant="outline"
                                size="xs"
                                className="flex items-center space-x-1"
                              >
                                <Eye className="w-3 h-3" />
                                <span>View</span>
                              </Button>
                              <Button
                                onClick={() => window.location.href = `/mapping?edit=${mapping.id}`}
                                variant="outline"
                                size="xs"
                                className="flex items-center space-x-1"
                              >
                                <Edit className="w-3 h-3" />
                                <span>Edit</span>
                              </Button>
                              <Button
                                onClick={() => setDeleteConfirm(mapping)}
                                variant="danger"
                                size="xs"
                                className="flex items-center space-x-1"
                              >
                                <Trash2 className="w-3 h-3" />
                                <span>Delete</span>
                              </Button>
                            </div>
                          </Table.Cell>
                        </Table.Row>
                      )
                    })}
                  </Table.Body>
                </Table>
              </div>
            )}
          </Card.Body>
        </Card>
      )}

      {/* View Mapping Modal */}
      {viewingMapping && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Mapping Details: {viewingMapping.metadata_table}
                </h3>
                <Button
                  onClick={() => setViewingMapping(null)}
                  variant="outline"
                  size="sm"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Field Mappings</h4>
                  <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                    {formatJson(viewingMapping.field_mappings)}
                  </pre>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Default Values</h4>
                  <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                    {formatJson(viewingMapping.default_values)}
                  </pre>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Extra Fields</h4>
                  <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                    {formatJson(viewingMapping.extra_fields)}
                  </pre>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Mapping Options</h4>
                  <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                    {formatJson(viewingMapping.mapping_options)}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
                <h3 className="text-lg font-semibold text-gray-900">Delete Mapping</h3>
              </div>
              
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete the mapping for <strong>{deleteConfirm.metadata_table}</strong>? 
                This action cannot be undone.
              </p>
              
              <div className="flex justify-end space-x-3">
                <Button
                  onClick={() => setDeleteConfirm(null)}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => deleteMapping(deleteConfirm.id)}
                  variant="danger"
                >
                  Delete Mapping
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
