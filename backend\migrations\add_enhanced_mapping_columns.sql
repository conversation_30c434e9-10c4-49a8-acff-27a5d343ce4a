-- Add enhanced mapping columns to support:
-- 1. Tags as IDs
-- 2. Extra Paperless fields without PP equivalents  
-- 3. Default values for any field

-- Add new columns to mappings table
ALTER TABLE mappings ADD COLUMN default_values TEXT;
ALTER TABLE mappings ADD COLUMN extra_fields TEXT;
ALTER TABLE mappings ADD COLUMN mapping_options TEXT;

-- Add comments for documentation
COMMENT ON COLUMN mappings.field_mappings IS 'JSON: PP column -> TechDoc field mappings';
COMMENT ON COLUMN mappings.default_values IS 'JSON: TechDoc field -> default value';
COMMENT ON COLUMN mappings.extra_fields IS 'JSON: TechDoc field -> value (no PP equivalent)';
COMMENT ON COLUMN mappings.mapping_options IS 'JSON: Additional mapping configuration';
