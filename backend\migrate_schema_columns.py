#!/usr/bin/env python3
"""
Migration script to add schema columns to plants table
"""

import sqlite3
import os
import sys

def migrate_database():
    """Add schema columns to plants table"""
    
    # Get database path
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'app.db')
    
    # Create data directory if it doesn't exist
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(plants)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'csdocs_schema' not in columns:
            print("Adding csdocs_schema column...")
            cursor.execute("ALTER TABLE plants ADD COLUMN csdocs_schema VARCHAR(128)")
            
        if 'metadata_schema' not in columns:
            print("Adding metadata_schema column...")
            cursor.execute("ALTER TABLE plants ADD COLUMN metadata_schema VARCHAR(128)")
        
        # Update existing plants with default schema values (username)
        cursor.execute("""
            UPDATE plants 
            SET csdocs_schema = username 
            WHERE csdocs_schema IS NULL AND username IS NOT NULL
        """)
        
        cursor.execute("""
            UPDATE plants 
            SET metadata_schema = username 
            WHERE metadata_schema IS NULL AND username IS NOT NULL
        """)
        
        conn.commit()
        print("✅ Database migration completed successfully!")
        
        # Show current plants table structure
        cursor.execute("PRAGMA table_info(plants)")
        columns = cursor.fetchall()
        print("\n📋 Current plants table structure:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        sys.exit(1)
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate_database()
