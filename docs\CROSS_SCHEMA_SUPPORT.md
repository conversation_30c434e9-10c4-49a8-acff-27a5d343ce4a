# Cross-Schema Support for Oracle Databases

## Overview

The GED to TechDoc migration tool now supports **cross-schema queries** for Oracle databases where the CSDOCS table and metadata tables (CSMETA_*) are located in different schemas.

## Why Cross-Schema Support?

In enterprise Oracle environments, it's common to have:
- **CSDOCS table** in a dedicated schema (e.g., `DOCUMENTS`, `FILESTORE`, `DOCMGMT`)
- **Metadata tables** (CSMETA_*) in a business logic schema (e.g., `METADATA`, `BUSINESS`, `PLANT_DATA`)

This separation provides:
- **Security isolation**: Different access controls for documents vs. metadata
- **Performance optimization**: Separate tablespaces and indexing strategies
- **Organizational clarity**: Clear separation of concerns

## Configuration

### Plant Configuration

When adding a plant, you can now specify:

1. **CSDOCS Schema**: Schema containing the CSDOCS table
2. **Metadata Schema**: Schema containing CSMETA_* tables

### Default Behavior

If schema fields are left blank, the system defaults to using the **connection username** as the schema name.

### Example Configuration

```
Plant Name: Plant A
Host: oracle-db.company.com
Port: 1521
Username: migration_user
CSDOCS Schema: DOCUMENTS
Metadata Schema: BUSINESS
```

## Technical Implementation

### Database Changes

Added two new columns to the `plants` table:
- `csdocs_schema` (VARCHAR(128)): Schema for CSDOCS table
- `metadata_schema` (VARCHAR(128)): Schema for CSMETA_* tables

### Query Changes

#### Table Discovery
```sql
-- CSDOCS tables
SELECT table_name, comments
FROM all_tab_comments
WHERE owner = :csdocs_schema AND table_name LIKE 'CSDOC%'

-- Metadata tables  
SELECT table_name, comments
FROM all_tab_comments
WHERE owner = :metadata_schema AND table_name LIKE 'CSMETA%'
```

#### Column Discovery
```sql
SELECT c.column_name, c.data_type, c.nullable, cc.comments
FROM all_tab_columns c
LEFT JOIN all_col_comments cc ON c.owner = cc.owner 
    AND c.table_name = cc.table_name 
    AND c.column_name = cc.column_name
WHERE c.owner = :schema AND c.table_name = :table_name
```

#### Document Queries
```sql
SELECT DKEY, DOCNAME, DOCPATH, RKEY
FROM {csdocs_schema}.CSDOC
WHERE 1=1
```

## Migration Process

### Automatic Migration

Run the migration script to add schema columns:
```bash
cd backend
python migrate_schema_columns.py
```

### Manual Migration

If needed, you can manually add the columns:
```sql
ALTER TABLE plants ADD COLUMN csdocs_schema VARCHAR(128);
ALTER TABLE plants ADD COLUMN metadata_schema VARCHAR(128);

-- Set defaults for existing plants
UPDATE plants SET csdocs_schema = username WHERE csdocs_schema IS NULL;
UPDATE plants SET metadata_schema = username WHERE metadata_schema IS NULL;
```

## Usage Examples

### Same Schema (Traditional)
```
Username: PLANT_USER
CSDOCS Schema: (blank - defaults to PLANT_USER)
Metadata Schema: (blank - defaults to PLANT_USER)
```
Queries: `PLANT_USER.CSDOC`, `PLANT_USER.CSMETA_PROJECT`

### Different Schemas
```
Username: MIGRATION_USER
CSDOCS Schema: DOCUMENTS
Metadata Schema: BUSINESS
```
Queries: `DOCUMENTS.CSDOC`, `BUSINESS.CSMETA_PROJECT`

### Mixed Configuration
```
Username: APP_USER
CSDOCS Schema: FILESTORE
Metadata Schema: (blank - defaults to APP_USER)
```
Queries: `FILESTORE.CSDOC`, `APP_USER.CSMETA_PROJECT`

## Permissions Required

The migration user needs appropriate permissions:

```sql
-- Grant access to CSDOCS schema
GRANT SELECT ON DOCUMENTS.CSDOC TO migration_user;

-- Grant access to metadata schema
GRANT SELECT ON BUSINESS.CSMETA_PROJECT TO migration_user;
GRANT SELECT ON BUSINESS.CSMETA_EQUIP TO migration_user;
-- ... (for all CSMETA_* tables)

-- Grant access to system views for discovery
GRANT SELECT ON ALL_TAB_COMMENTS TO migration_user;
GRANT SELECT ON ALL_TAB_COLUMNS TO migration_user;
GRANT SELECT ON ALL_COL_COMMENTS TO migration_user;
```

## Troubleshooting

### Common Issues

1. **"Table or view does not exist"**
   - Check schema names are correct
   - Verify user has SELECT permissions
   - Ensure tables exist in specified schemas

2. **"No tables found"**
   - Verify schema names match exactly (case-sensitive)
   - Check if tables follow naming convention (CSDOC*, CSMETA_*)

3. **"Access denied"**
   - Grant necessary permissions to migration user
   - Check if schemas are accessible

### Debug Tips

1. **Test schema access**:
   ```sql
   SELECT COUNT(*) FROM DOCUMENTS.CSDOC;
   SELECT COUNT(*) FROM BUSINESS.CSMETA_PROJECT;
   ```

2. **List available tables**:
   ```sql
   SELECT owner, table_name 
   FROM all_tables 
   WHERE table_name LIKE 'CSDOC%' OR table_name LIKE 'CSMETA%';
   ```

3. **Check permissions**:
   ```sql
   SELECT * FROM user_tab_privs 
   WHERE table_name LIKE 'CSDOC%' OR table_name LIKE 'CSMETA%';
   ```

## Benefits

✅ **Flexible Architecture**: Supports various Oracle schema configurations
✅ **Security Compliance**: Respects existing security boundaries
✅ **Performance Optimized**: Leverages existing schema optimizations
✅ **Backward Compatible**: Works with single-schema setups
✅ **Auto-Discovery**: Automatically finds tables across schemas
✅ **Easy Configuration**: Simple UI for schema specification
