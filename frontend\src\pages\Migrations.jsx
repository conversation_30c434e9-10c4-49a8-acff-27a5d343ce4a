import React, { useEffect, useState } from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'
import { <PERSON>, <PERSON><PERSON>, LoadingSpinner, Badge } from '../components/ui'
import {
  Database,
  Play,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Eye
} from 'lucide-react'

export default function Migrations() {
  const selectedPlant = useStore(s => s.selectedPlant)
  const [mappings, setMappings] = useState([])
  const [runs, setRuns] = useState([])
  const [selectedMapping, setSelectedMapping] = useState(null)
  const [loading, setLoading] = useState(false)
  const [loadingRuns, setLoadingRuns] = useState(false)
  const [starting, setStarting] = useState(false)

  useEffect(() => {
    loadRuns()
    if (selectedPlant) {
      loadMappings()
    }
  }, [selectedPlant])

  async function loadMappings() {
    if (!selectedPlant) return

    setLoading(true)
    try {
      const response = await api.get(`/mappings/${selectedPlant.id}`)
      setMappings(response.data)
    } catch (e) {
      console.error(e)
    } finally {
      setLoading(false)
    }
  }

  async function loadRuns() {
    setLoadingRuns(true)
    try {
      const response = await api.get('/migrate/runs')
      setRuns(response.data)
    } catch (e) {
      console.error(e)
    } finally {
      setLoadingRuns(false)
    }
  }

  async function startMigration() {
    if (!selectedMapping) {
      alert('Please select a mapping first')
      return
    }

    setStarting(true)
    try {
      const response = await api.post('/migrate/start', null, {
        params: {
          plant_id: selectedPlant.id,
          mapping_id: selectedMapping.id
        }
      })
      alert(`Migration started with task ID: ${response.data.task_id}`)
      await loadRuns()
    } catch (e) {
      console.error(e)
      alert('Failed to start migration')
    } finally {
      setStarting(false)
    }
  }

  async function viewLogs(run) {
    try {
      const response = await api.get(`/migrate/runs/${run.id}/logs`)
      alert(`Logs count: ${response.data.length}`)
    } catch (e) {
      console.error(e)
      alert('Failed to load logs')
    }
  }

  function getStatusIcon(status) {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return <CheckCircle className="w-4 h-4 text-success-600" />
      case 'failed':
      case 'error':
        return <XCircle className="w-4 h-4 text-danger-600" />
      case 'running':
      case 'in_progress':
        return <Clock className="w-4 h-4 text-warning-600" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />
    }
  }

  function getStatusBadge(status) {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return <Badge variant="success">{status}</Badge>
      case 'failed':
      case 'error':
        return <Badge variant="danger">{status}</Badge>
      case 'running':
      case 'in_progress':
        return <Badge variant="warning">{status}</Badge>
      default:
        return <Badge variant="info">{status || 'Unknown'}</Badge>
    }
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
          <Database className="w-5 h-5 text-primary-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Data Migrations</h1>
          <p className="text-gray-600">Run and monitor data migrations to TechDoc</p>
        </div>
      </div>

      {!selectedPlant ? (
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Plant Selected</h3>
              <p className="text-gray-600 mb-4">
                Please select a plant from the Plants page to run migrations.
              </p>
              <Button variant="primary" onClick={() => window.location.href = '/plants'}>
                Go to Plants
              </Button>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <>
          {/* Mappings Selection */}
          <Card>
            <Card.Header>
              <div className="flex items-center justify-between">
                <Card.Title className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Available Mappings</span>
                </Card.Title>
                <Badge variant="info">{mappings.length} mappings</Badge>
              </div>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="md" />
                  <span className="ml-3 text-gray-600">Loading mappings...</span>
                </div>
              ) : mappings.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 mb-4">No mappings found for this plant.</p>
                  <Button variant="primary" onClick={() => window.location.href = '/mapping'}>
                    Create Mapping
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {mappings.map((mapping) => (
                    <div
                      key={mapping.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        selectedMapping?.id === mapping.id
                          ? 'border-primary-300 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedMapping(mapping)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Database className="w-5 h-5 text-gray-400" />
                          <div>
                            <h4 className="font-medium text-gray-900">{mapping.metadata_table}</h4>
                            <p className="text-sm text-gray-600">
                              {Object.keys(mapping.field_mappings || {}).length} field mappings
                            </p>
                          </div>
                        </div>
                        {selectedMapping?.id === mapping.id && (
                          <CheckCircle className="w-5 h-5 text-primary-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Migration Controls */}
          {selectedMapping && (
            <Card>
              <Card.Header>
                <Card.Title className="flex items-center space-x-2">
                  <Play className="w-5 h-5" />
                  <span>Start Migration</span>
                </Card.Title>
              </Card.Header>
              <Card.Body>
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Ready to migrate: {selectedMapping.metadata_table}</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      This will start migrating data from the selected table to TechDoc using the configured field mappings.
                    </p>
                  </div>
                  <Button
                    onClick={startMigration}
                    loading={starting}
                    disabled={starting}
                    variant="success"
                    className="flex items-center space-x-2"
                  >
                    <Play className="w-4 h-4" />
                    <span>Start Migration</span>
                  </Button>
                </div>
              </Card.Body>
            </Card>
          )}

          {/* Migration Runs */}
          <Card>
            <Card.Header>
              <div className="flex items-center justify-between">
                <Card.Title className="flex items-center space-x-2">
                  <Clock className="w-5 h-5" />
                  <span>Migration History</span>
                </Card.Title>
                <div className="flex items-center space-x-3">
                  <Badge variant="info">{runs.length} runs</Badge>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={loadRuns}
                    loading={loadingRuns}
                    className="flex items-center space-x-1"
                  >
                    <RefreshCw className="w-3 h-3" />
                    <span>Refresh</span>
                  </Button>
                </div>
              </div>
            </Card.Header>
            <Card.Body>
              {loadingRuns ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="md" />
                  <span className="ml-3 text-gray-600">Loading migration runs...</span>
                </div>
              ) : runs.length === 0 ? (
                <div className="text-center py-8">
                  <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">No migration runs found.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {runs.map((run) => (
                    <div key={run.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(run.status)}
                          <div>
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-gray-900">Run #{run.id}</h4>
                              {getStatusBadge(run.status)}
                            </div>
                            <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                              <span>Total: {run.total || 0}</span>
                              <span className="text-success-600">Succeeded: {run.succeeded || 0}</span>
                              <span className="text-danger-600">Failed: {run.failed || 0}</span>
                            </div>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => viewLogs(run)}
                          className="flex items-center space-x-1"
                        >
                          <Eye className="w-3 h-3" />
                          <span>View Logs</span>
                        </Button>
                      </div>

                      {/* Progress Bar */}
                      {run.total > 0 && (
                        <div className="mt-3">
                          <div className="flex justify-between text-xs text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{Math.round(((run.succeeded + run.failed) / run.total) * 100)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${Math.min(((run.succeeded + run.failed) / run.total) * 100, 100)}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Card.Body>
          </Card>
        </>
      )}
    </div>
  )
}
