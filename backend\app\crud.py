from sqlalchemy.orm import Session
from . import models, schemas, security
import json, os

def create_plant(db: Session, plant: schemas.PlantCreate):
    p = models.Plant(
        name=plant.name,
        host=plant.host or "",
        port=plant.port or "",
        service_name=plant.service_name or "",
        username=plant.username or "",
        password_enc=security.encrypt_value(plant.password) if plant.password else None,
        storage_path=plant.storage_path or "",
        csdocs_schema=plant.csdocs_schema or "",
        metadata_schema=plant.metadata_schema or "",
        notes=plant.notes or ""
    )
    db.add(p); db.commit(); db.refresh(p); return p

def update_plant(db: Session, plant_id: int, plant_update: schemas.PlantUpdate):
    p = db.query(models.Plant).filter(models.Plant.id == plant_id).first()
    if not p:
        return None

    # Update fields if provided
    if plant_update.name is not None:
        p.name = plant_update.name
    if plant_update.host is not None:
        p.host = plant_update.host
    if plant_update.port is not None:
        p.port = plant_update.port
    if plant_update.service_name is not None:
        p.service_name = plant_update.service_name
    if plant_update.username is not None:
        p.username = plant_update.username
    if plant_update.password is not None:
        p.password_enc = security.encrypt_value(plant_update.password) if plant_update.password else None
    if plant_update.storage_path is not None:
        p.storage_path = plant_update.storage_path
    if plant_update.csdocs_schema is not None:
        p.csdocs_schema = plant_update.csdocs_schema
    if plant_update.metadata_schema is not None:
        p.metadata_schema = plant_update.metadata_schema
    if plant_update.notes is not None:
        p.notes = plant_update.notes
    if plant_update.active is not None:
        p.active = plant_update.active

    db.commit(); db.refresh(p); return p

def delete_plant(db: Session, plant_id: int):
    p = db.query(models.Plant).filter(models.Plant.id == plant_id).first()
    if not p:
        return None

    # Delete related mappings first
    db.query(models.Mapping).filter(models.Mapping.plant_id == plant_id).delete()

    # Delete the plant
    db.delete(p)
    db.commit()
    return True

def get_plants(db: Session): return db.query(models.Plant).all()

def get_plant(db: Session, plant_id: int): return db.query(models.Plant).filter(models.Plant.id==plant_id).first()

def save_mapping(db: Session, mapping: schemas.MappingIn):
    m = db.query(models.Mapping).filter(models.Mapping.plant_id==mapping.plant_id, models.Mapping.metadata_table==mapping.metadata_table).first()
    if m is None:
        m = models.Mapping(plant_id=mapping.plant_id, metadata_table=mapping.metadata_table, field_mappings=json.dumps(mapping.field_mappings))
        db.add(m)
    else:
        m.field_mappings = json.dumps(mapping.field_mappings)
    db.commit(); db.refresh(m); return m

def save_enhanced_mapping(db: Session, mapping: schemas.MappingIn):
    """Save enhanced mapping with tags as IDs, extra fields, and default values"""
    m = db.query(models.Mapping).filter(
        models.Mapping.plant_id == mapping.plant_id,
        models.Mapping.metadata_table == mapping.metadata_table
    ).first()

    # Extract enhanced mapping data
    field_mappings = mapping.field_mappings
    if isinstance(field_mappings, dict) and 'field_mappings' in field_mappings:
        # New enhanced format (nested from frontend)
        enhanced_data = field_mappings
        actual_field_mappings = enhanced_data.get('field_mappings', {})
        default_values = enhanced_data.get('default_values', {})
        extra_fields = enhanced_data.get('extra_fields', {})
        mapping_options = enhanced_data.get('mapping_options', {})
    else:
        # Direct format (from schema)
        actual_field_mappings = field_mappings
        default_values = mapping.default_values or {}
        extra_fields = mapping.extra_fields or {}
        mapping_options = mapping.mapping_options or {}

    if m is None:
        m = models.Mapping(
            plant_id=mapping.plant_id,
            metadata_table=mapping.metadata_table,
            field_mappings=json.dumps(actual_field_mappings),
            default_values=json.dumps(default_values) if default_values else None,
            extra_fields=json.dumps(extra_fields) if extra_fields else None,
            mapping_options=json.dumps(mapping_options) if mapping_options else None
        )
        db.add(m)
    else:
        m.field_mappings = json.dumps(actual_field_mappings)
        m.default_values = json.dumps(default_values) if default_values else None
        m.extra_fields = json.dumps(extra_fields) if extra_fields else None
        m.mapping_options = json.dumps(mapping_options) if mapping_options else None

    db.commit()
    db.refresh(m)
    return m

def get_mappings_for_plant(db: Session, plant_id: int):
    return db.query(models.Mapping).filter(models.Mapping.plant_id==plant_id).all()

def get_mapping(db: Session, mapping_id: int):
    """Get a specific mapping by ID"""
    return db.query(models.Mapping).filter(models.Mapping.id == mapping_id).first()

def update_mapping(db: Session, mapping_id: int, mapping_update: schemas.MappingUpdate):
    """Update a specific mapping"""
    m = db.query(models.Mapping).filter(models.Mapping.id == mapping_id).first()
    if not m:
        return None

    # Update fields if provided
    if mapping_update.metadata_table is not None:
        m.metadata_table = mapping_update.metadata_table
    if mapping_update.field_mappings is not None:
        m.field_mappings = json.dumps(mapping_update.field_mappings)
    if mapping_update.default_values is not None:
        m.default_values = json.dumps(mapping_update.default_values) if mapping_update.default_values else None
    if mapping_update.extra_fields is not None:
        m.extra_fields = json.dumps(mapping_update.extra_fields) if mapping_update.extra_fields else None
    if mapping_update.mapping_options is not None:
        m.mapping_options = json.dumps(mapping_update.mapping_options) if mapping_update.mapping_options else None

    db.commit()
    db.refresh(m)
    return m

def delete_mapping(db: Session, mapping_id: int):
    """Delete a specific mapping"""
    m = db.query(models.Mapping).filter(models.Mapping.id == mapping_id).first()
    if not m:
        return None

    # Check if mapping is being used in any migration runs
    runs = db.query(models.MigrationRun).filter(models.MigrationRun.mapping_id == mapping_id).count()
    if runs > 0:
        return False  # Cannot delete mapping that's being used

    db.delete(m)
    db.commit()
    return True

def get_all_mappings(db: Session):
    """Get all mappings across all plants"""
    return db.query(models.Mapping).all()

def create_migration_run(db: Session, plant_id: int, mapping_id: int):
    run = models.MigrationRun(plant_id=plant_id, mapping_id=mapping_id, status='pending')
    db.add(run); db.commit(); db.refresh(run); return run

def update_run_status(db: Session, run_id: int, **kwargs):
    r = db.query(models.MigrationRun).filter(models.MigrationRun.id==run_id).first()
    for k,v in kwargs.items(): setattr(r, k, v)
    db.commit(); db.refresh(r); return r

def add_run_log(db: Session, run_id: int, doc_dkey: int, doc_name: str, status: str, message: str | None = None, attempts: int = 0, paperless_id: int | None = None):
    log = models.MigrationLog(run_id=run_id, doc_dkey=doc_dkey, doc_name=doc_name, status=status, message=message, attempts=attempts, paperless_id=paperless_id)
    db.add(log); db.commit(); db.refresh(log); return log

def update_log_attempts(db: Session, log_id: int, attempts: int, status: str = None, message: str = None, paperless_id: int | None = None):
    l = db.query(models.MigrationLog).filter(models.MigrationLog.id==log_id).first()
    if l is None: return None
    l.attempts = attempts
    if status: l.status = status
    if message: l.message = message
    if paperless_id is not None: l.paperless_id = paperless_id
    db.commit(); db.refresh(l); return l

def get_run_logs(db: Session, run_id: int): return db.query(models.MigrationLog).filter(models.MigrationLog.run_id==run_id).all()

def get_log(db: Session, log_id: int): return db.query(models.MigrationLog).filter(models.MigrationLog.id==log_id).first()

# upload helper (simple wrapper)
from .crud_upload_helper import _upload_document_helper
