import React, { useState } from 'react'
import { api } from '../lib/api'
import { Card, Button, Input, Badge } from '../components/ui'
import { FileSearch, TestTube, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

export default function Paperless() {
  const [form, setForm] = useState({ api_url: '', token: '' })
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState(null)
  const [errors, setErrors] = useState({})

  function handleChange(e) {
    const { name, value } = e.target
    setForm({ ...form, [name]: value })
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
    // Clear test result when form changes
    if (testResult) {
      setTestResult(null)
    }
  }

  function validateForm() {
    const newErrors = {}
    if (!form.api_url.trim()) newErrors.api_url = 'API URL is required'
    if (!form.token.trim()) newErrors.token = 'Admin token is required'

    // Basic URL validation
    if (form.api_url && !form.api_url.match(/^https?:\/\/.+/)) {
      newErrors.api_url = 'Please enter a valid URL (http:// or https://)'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  async function handleTest() {
    if (!validateForm()) return

    setTesting(true)
    setTestResult(null)

    try {
      const response = await api.post('/paperless/test', form)
      setTestResult({
        success: true,
        data: response.data,
        message: 'Connection successful! TechDoc API is responding correctly.'
      })
    } catch (error) {
      setTestResult({
        success: false,
        error: error.response?.data?.detail || error.message || 'Connection failed',
        message: 'Failed to connect to TechDoc API. Please check your configuration.'
      })
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
          <FileSearch className="w-5 h-5 text-primary-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">TechDoc Configuration</h1>
          <p className="text-gray-600">Configure your TechDoc API connection</p>
        </div>
      </div>

      {/* Configuration Form */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center space-x-2">
            <FileSearch className="w-5 h-5" />
            <span>API Configuration</span>
          </Card.Title>
        </Card.Header>
        <Card.Body>
          <div className="space-y-4">
            <Input
              label="TechDoc API URL"
              name="api_url"
              value={form.api_url}
              onChange={handleChange}
              placeholder="https://paperless.example.com/api"
              error={errors.api_url}
              required
            />

            <Input
              label="Admin Token"
              name="token"
              type="password"
              value={form.token}
              onChange={handleChange}
              placeholder="Your TechDoc admin token"
              error={errors.token}
              required
            />

            <div className="flex justify-end">
              <Button
                onClick={handleTest}
                loading={testing}
                disabled={testing}
                className="flex items-center space-x-2"
              >
                <TestTube className="w-4 h-4" />
                <span>Test Connection</span>
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Test Results */}
      {testResult && (
        <Card>
          <Card.Header>
            <Card.Title className="flex items-center space-x-2">
              {testResult.success ? (
                <CheckCircle className="w-5 h-5 text-success-600" />
              ) : (
                <XCircle className="w-5 h-5 text-danger-600" />
              )}
              <span>Connection Test Results</span>
            </Card.Title>
          </Card.Header>
          <Card.Body>
            <div className={`p-4 rounded-lg border ${
              testResult.success
                ? 'bg-success-50 border-success-200'
                : 'bg-danger-50 border-danger-200'
            }`}>
              <div className="flex items-start space-x-3">
                {testResult.success ? (
                  <CheckCircle className="w-5 h-5 text-success-600 mt-0.5" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-danger-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <p className={`font-medium ${
                    testResult.success ? 'text-success-800' : 'text-danger-800'
                  }`}>
                    {testResult.message}
                  </p>
                  {testResult.success && testResult.data && (
                    <div className="mt-3 space-y-2">
                      <Badge variant="success">API Version: {testResult.data.version || 'Unknown'}</Badge>
                      {testResult.data.user && (
                        <div className="text-sm text-success-700">
                          Connected as: {testResult.data.user.username}
                        </div>
                      )}
                    </div>
                  )}
                  {!testResult.success && testResult.error && (
                    <div className="mt-2 text-sm text-danger-700 font-mono bg-danger-100 p-2 rounded">
                      {testResult.error}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card.Body>
        </Card>
      )}
    </div>
  )
}
