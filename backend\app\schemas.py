from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
class PlantBase(BaseModel):
    name: str
    host: Optional[str] = None
    port: Optional[str] = None
    service_name: Optional[str] = None
    username: Optional[str] = None
    storage_path: Optional[str] = None
    csdocs_schema: Optional[str] = None
    metadata_schema: Optional[str] = None
    notes: Optional[str] = None

class PlantCreate(PlantBase):
    password: Optional[str] = None

class PlantUpdate(BaseModel):
    name: Optional[str] = None
    host: Optional[str] = None
    port: Optional[str] = None
    service_name: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    storage_path: Optional[str] = None
    csdocs_schema: Optional[str] = None
    metadata_schema: Optional[str] = None
    notes: Optional[str] = None
    active: Optional[bool] = None

class PlantOut(PlantBase):
    id: int
    active: bool
    class Config:
        from_attributes = True
class MappingIn(BaseModel):
    plant_id: int
    metadata_table: str
    field_mappings: Dict[str, Any]
    default_values: Optional[Dict[str, Any]] = None
    extra_fields: Optional[Dict[str, Any]] = None
    mapping_options: Optional[Dict[str, Any]] = None

class MappingUpdate(BaseModel):
    metadata_table: Optional[str] = None
    field_mappings: Optional[Dict[str, Any]] = None
    default_values: Optional[Dict[str, Any]] = None
    extra_fields: Optional[Dict[str, Any]] = None
    mapping_options: Optional[Dict[str, Any]] = None

class MappingOut(BaseModel):
    id: int
    plant_id: int
    metadata_table: str
    field_mappings: str  # JSON string
    default_values: Optional[str] = None  # JSON string
    extra_fields: Optional[str] = None  # JSON string
    mapping_options: Optional[str] = None  # JSON string
    created_at: datetime

    class Config:
        from_attributes = True
class MigrationRunOut(BaseModel):
    id: int
    plant_id: int
    mapping_id: int
    status: str
    total: int
    succeeded: int
    failed: int
    class Config:
        from_attributes = True
class MigrationLogOut(BaseModel):
    id: int
    run_id: int
    doc_name: Optional[str]
    status: str
    message: Optional[str]
    attempts: int
    created_at: datetime
    class Config:
        from_attributes = True
