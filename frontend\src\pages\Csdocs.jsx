import React, { useEffect, useState } from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'
import { Card, Button, Input, Table, LoadingSpinner, Badge } from '../components/ui'
import { FileText, Search, AlertCircle, Database, RefreshCw, CheckCircle, XCircle } from 'lucide-react'

export default function Csdocs() {
  const selectedPlant = useStore(s => s.selectedPlant)
  const [docs, setDocs] = useState([])
  const [query, setQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [searching, setSearching] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (selectedPlant) {
      loadDocs()
    }
  }, [selectedPlant])

  async function loadDocs() {
    if (!selectedPlant) return

    setLoading(true)
    setError(null)
    try {
      const response = await api.get('/csdocs', {
        params: { plant_id: selectedPlant.id }
      })
      setDocs(response.data)
    } catch (e) {
      console.error(e)
      setError('Failed to load documents. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  async function handleSearch() {
    if (!selectedPlant) return

    setSearching(true)
    setError(null)
    try {
      const response = await api.get('/csdocs', {
        params: { plant_id: selectedPlant.id, q: query }
      })
      setDocs(response.data)
    } catch (e) {
      console.error(e)
      setError('Search failed. Please try again.')
    } finally {
      setSearching(false)
    }
  }

  function handleKeyPress(e) {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  function handleClearSearch() {
    setQuery('')
    loadDocs()
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
          <FileText className="w-5 h-5 text-primary-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">CSDOCS Browser</h1>
          <p className="text-gray-600">Browse and search documents in your plant database</p>
        </div>
      </div>

      {!selectedPlant ? (
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Plant Selected</h3>
              <p className="text-gray-600 mb-4">
                Please select a plant from the Plants page to browse documents.
              </p>
              <Button variant="primary" onClick={() => window.location.href = '/plants'}>
                Go to Plants
              </Button>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <>
          {/* Search Section */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center space-x-2">
                <Search className="w-5 h-5" />
                <span>Search Documents</span>
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="flex space-x-3">
                <div className="flex-1">
                  <Input
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Search by document name or path..."
                    className="w-full"
                  />
                </div>
                <Button
                  onClick={handleSearch}
                  loading={searching}
                  disabled={searching}
                  className="flex items-center space-x-2"
                >
                  <Search className="w-4 h-4" />
                  <span>Search</span>
                </Button>
                {query && (
                  <Button
                    variant="secondary"
                    onClick={handleClearSearch}
                    className="flex items-center space-x-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>Clear</span>
                  </Button>
                )}
              </div>

              {error && (
                <div className="mt-4 p-3 bg-danger-50 border border-danger-200 rounded-lg flex items-center space-x-2">
                  <AlertCircle className="w-5 h-5 text-danger-600" />
                  <span className="text-danger-700">{error}</span>
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Documents Table */}
          <Card>
            <Card.Header>
              <div className="flex items-center justify-between">
                <Card.Title className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Documents</span>
                </Card.Title>
                <div className="flex items-center space-x-3">
                  <Badge variant="info">{docs.length} documents</Badge>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={loadDocs}
                    loading={loading}
                    className="flex items-center space-x-1"
                  >
                    <RefreshCw className="w-3 h-3" />
                    <span>Refresh</span>
                  </Button>
                </div>
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <LoadingSpinner size="lg" />
                  <span className="ml-3 text-gray-600">Loading documents...</span>
                </div>
              ) : docs.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {query ? 'No documents found' : 'No documents available'}
                  </h3>
                  <p className="text-gray-600">
                    {query
                      ? 'Try adjusting your search terms or clear the search to see all documents.'
                      : 'There are no documents in the selected plant database.'
                    }
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <Table.Header>
                      <Table.Row>
                        <Table.HeaderCell>Document Key</Table.HeaderCell>
                        <Table.HeaderCell>Document Name</Table.HeaderCell>
                        <Table.HeaderCell>Original Path</Table.HeaderCell>
                        <Table.HeaderCell>Docker Path</Table.HeaderCell>
                        <Table.HeaderCell>Status</Table.HeaderCell>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      {docs.map((doc) => (
                        <Table.Row key={doc.DKEY}>
                          <Table.Cell>
                            <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                              {doc.DKEY}
                            </span>
                          </Table.Cell>
                          <Table.Cell>
                            <div className="flex items-center space-x-2">
                              <FileText className="w-4 h-4 text-gray-400" />
                              <span className="font-medium">{doc.DOCNAME}</span>
                            </div>
                          </Table.Cell>
                          <Table.Cell>
                            <span className="font-mono text-xs text-gray-600 break-all">
                              {doc.DOCPATH}
                            </span>
                          </Table.Cell>
                          <Table.Cell>
                            {doc.RESOLVED_PATH ? (
                              <span className="font-mono text-xs text-blue-600 break-all">
                                {doc.RESOLVED_PATH}
                              </span>
                            ) : (
                              <span className="text-xs text-gray-400">Not resolved</span>
                            )}
                          </Table.Cell>
                          <Table.Cell>
                            {doc.FILE_EXISTS === true ? (
                              <Badge variant="success" className="text-xs">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Available
                              </Badge>
                            ) : doc.FILE_EXISTS === false ? (
                              <Badge variant="danger" className="text-xs">
                                <XCircle className="w-3 h-3 mr-1" />
                                Missing
                              </Badge>
                            ) : (
                              <Badge variant="secondary" className="text-xs">
                                Unknown
                              </Badge>
                            )}
                          </Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>
        </>
      )}
    </div>
  )
}
