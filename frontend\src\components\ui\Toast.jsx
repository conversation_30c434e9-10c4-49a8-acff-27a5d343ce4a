import React, { useState, useEffect } from 'react'
import { CheckCircle, AlertCircle, Info, AlertTriangle, X } from 'lucide-react'

const Toast = ({ 
  message, 
  variant = 'info', 
  duration = 5000, 
  onClose,
  className = '' 
}) => {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        setTimeout(() => onClose && onClose(), 300) // Wait for animation
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [duration, onClose])

  const variants = {
    success: {
      container: 'bg-success-50 border-success-200 text-success-800',
      icon: CheckCircle,
      iconColor: 'text-success-600'
    },
    danger: {
      container: 'bg-danger-50 border-danger-200 text-danger-800',
      icon: AlertCircle,
      iconColor: 'text-danger-600'
    },
    warning: {
      container: 'bg-warning-50 border-warning-200 text-warning-800',
      icon: Alert<PERSON>riangle,
      iconColor: 'text-warning-600'
    },
    info: {
      container: 'bg-primary-50 border-primary-200 text-primary-800',
      icon: Info,
      iconColor: 'text-primary-600'
    }
  }

  const config = variants[variant]
  const Icon = config.icon

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => onClose && onClose(), 300)
  }

  return (
    <div className={`
      fixed top-4 right-4 z-50 max-w-sm w-full transform transition-all duration-300 ease-in-out
      ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      ${className}
    `}>
      <div className={`p-4 border rounded-lg shadow-lg ${config.container}`}>
        <div className="flex items-start space-x-3">
          <Icon className={`w-5 h-5 ${config.iconColor} mt-0.5 flex-shrink-0`} />
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium">{message}</p>
          </div>
          <button
            onClick={handleClose}
            className={`${config.iconColor} hover:opacity-75 flex-shrink-0`}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default Toast
