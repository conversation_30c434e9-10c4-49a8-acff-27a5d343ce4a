import { useEffect, useState } from 'react'
import { api } from '../lib/api'
import { <PERSON>, LoadingSpinner, <PERSON>ge, <PERSON><PERSON> } from '../components/ui'
import { 
  Factory, 
  Database, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  BarChart3
} from 'lucide-react'

export default function Dashboard() {
  const [loading, setLoading] = useState(true)
  const [plants, setPlants] = useState([])
  const [migrationRuns, setMigrationRuns] = useState([])
  const [stats, setStats] = useState({
    totalPlants: 0,
    activePlants: 0,
    totalMigrations: 0,
    successfulMigrations: 0,
    failedMigrations: 0,
    runningMigrations: 0
  })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    setLoading(true)
    try {
      // Fetch plants, migration runs, and dashboard stats in parallel
      const [plantsResponse, migrationsResponse, statsResponse] = await Promise.all([
        api.get('/plants'),
        api.get('/migrate/runs'),
        api.get('/dashboard/stats')
      ])

      const plantsData = plantsResponse.data
      const migrationsData = migrationsResponse.data
      const statsData = statsResponse.data

      setPlants(plantsData)
      setMigrationRuns(migrationsData)

      // Use backend-calculated statistics
      setStats({
        totalPlants: statsData.plants.total,
        activePlants: statsData.plants.active,
        totalMigrations: statsData.migrations.total,
        successfulMigrations: statsData.migrations.successful,
        failedMigrations: statsData.migrations.failed,
        runningMigrations: statsData.migrations.running,
        totalDocuments: statsData.documents.total,
        migratedDocuments: statsData.documents.migrated,
        failedDocuments: statsData.documents.failed
      })

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success" className="flex items-center space-x-1">
          <CheckCircle className="w-3 h-3" />
          <span>Completed</span>
        </Badge>
      case 'failed':
        return <Badge variant="danger" className="flex items-center space-x-1">
          <XCircle className="w-3 h-3" />
          <span>Failed</span>
        </Badge>
      case 'running':
        return <Badge variant="warning" className="flex items-center space-x-1">
          <Clock className="w-3 h-3" />
          <span>Running</span>
        </Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPlantName = (plantId) => {
    const plant = plants.find(p => p.id === plantId)
    return plant ? plant.name : `Plant ${plantId}`
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString()
  }

  const calculateSuccessRate = () => {
    if (stats.totalMigrations === 0) return 0
    return Math.round((stats.successfulMigrations / stats.totalMigrations) * 100)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
        <span className="ml-3 text-gray-600">Loading dashboard...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Migration Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor migration progress across all plants</p>
        </div>
        <Button
          onClick={fetchDashboardData}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Refresh</span>
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <Card.Body className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Plants</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalPlants}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.activePlants} active
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Factory className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </Card.Body>
        </Card>

        <Card>
          <Card.Body className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Migrations</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalMigrations}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.runningMigrations} running
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Database className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </Card.Body>
        </Card>

        <Card>
          <Card.Body className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">{calculateSuccessRate()}%</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.successfulMigrations} successful
                </p>
              </div>
              <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-emerald-600" />
              </div>
            </div>
          </Card.Body>
        </Card>

        <Card>
          <Card.Body className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Failed Migrations</p>
                <p className="text-2xl font-bold text-gray-900">{stats.failedMigrations}</p>
                <p className="text-xs text-gray-500 mt-1">
                  Require attention
                </p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Plants Overview */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center space-x-2">
            <Factory className="w-5 h-5" />
            <span>Plants Overview</span>
          </Card.Title>
        </Card.Header>
        <Card.Body className="p-0">
          {plants.length === 0 ? (
            <div className="text-center py-12">
              <Factory className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No plants configured</h3>
              <p className="text-gray-600">Add plants to start monitoring migrations.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plant
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Migrations
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Migration
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {plants.map((plant) => {
                    const plantMigrations = migrationRuns.filter(m => m.plant_id === plant.id)
                    const lastMigration = plantMigrations.sort((a, b) => 
                      new Date(b.started_at || 0) - new Date(a.started_at || 0)
                    )[0]
                    
                    return (
                      <tr key={plant.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                              <Factory className="w-4 h-4 text-primary-600" />
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-900">{plant.name}</div>
                              <div className="text-sm text-gray-500">{plant.host}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge variant={plant.active ? "success" : "secondary"}>
                            {plant.active ? "Active" : "Inactive"}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {plantMigrations.length} total
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {lastMigration ? (
                            <div>
                              {getStatusBadge(lastMigration.status)}
                              <div className="text-xs text-gray-500 mt-1">
                                {formatDate(lastMigration.started_at)}
                              </div>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">No migrations</span>
                          )}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Recent Migrations */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5" />
            <span>Recent Migrations</span>
          </Card.Title>
        </Card.Header>
        <Card.Body className="p-0">
          {migrationRuns.length === 0 ? (
            <div className="text-center py-12">
              <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No migrations yet</h3>
              <p className="text-gray-600">Start your first migration to see progress here.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plant
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Started
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {migrationRuns.slice(0, 10).map((run) => {
                    const progressPercent = run.total > 0 ? Math.round((run.succeeded / run.total) * 100) : 0
                    const duration = run.started_at && run.finished_at
                      ? Math.round((new Date(run.finished_at) - new Date(run.started_at)) / 1000 / 60)
                      : null

                    return (
                      <tr key={run.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                              <Factory className="w-4 h-4 text-primary-600" />
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {getPlantName(run.plant_id)}
                              </div>
                              <div className="text-sm text-gray-500">Run #{run.id}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(run.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                              <div
                                className="bg-primary-600 h-2 rounded-full"
                                style={{ width: `${progressPercent}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-900">
                              {run.succeeded}/{run.total}
                            </span>
                          </div>
                          {run.failed > 0 && (
                            <div className="text-xs text-red-600 mt-1">
                              {run.failed} failed
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(run.started_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {duration ? `${duration} min` : run.status === 'running' ? 'In progress' : 'N/A'}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          )}
        </Card.Body>
      </Card>
    </div>
  )
}
