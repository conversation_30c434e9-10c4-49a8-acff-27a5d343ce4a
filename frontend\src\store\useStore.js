import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export const useStore = create(
  persist(
    (set, get) => ({
      selectedPlant: null,
      setSelectedPlant: (plant) => {
        set({ selectedPlant: plant })
        // Store in localStorage for persistence
        if (plant) {
          localStorage.setItem('selectedPlant', JSON.stringify(plant))
        } else {
          localStorage.removeItem('selectedPlant')
        }
      },
      clearSelectedPlant: () => {
        set({ selectedPlant: null })
        localStorage.removeItem('selectedPlant')
      },
      // Initialize from localStorage on app start
      initializeSelectedPlant: () => {
        try {
          const stored = localStorage.getItem('selectedPlant')
          if (stored) {
            const plant = JSON.parse(stored)
            set({ selectedPlant: plant })
          }
        } catch (error) {
          console.warn('Failed to load selected plant from localStorage:', error)
          localStorage.removeItem('selectedPlant')
        }
      }
    }),
    {
      name: 'pp-to-paperless-store',
      partialize: (state) => ({ selectedPlant: state.selectedPlant })
    }
  )
)
