#!/usr/bin/env python3
"""
Test script to verify the metadata preview endpoint
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_preview_endpoint():
    """Test the metadata preview endpoint"""
    
    print("🧪 Testing Metadata Preview Endpoint")
    print("=" * 50)
    
    # Test with demo data (plant_id=1, table=CSMETA_PROJECT)
    plant_id = 1
    table_name = "CSMETA_PROJECT"
    
    print(f"\n1️⃣ Testing preview for Plant {plant_id}, Table {table_name}...")
    
    try:
        response = requests.get(f"{BASE_URL}/metadata/preview/{plant_id}/{table_name}?limit=10")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Preview data retrieved successfully!")
            print(f"   Table: {data.get('table_name')}")
            print(f"   Total Rows: {data.get('total_rows')}")
            print(f"   Columns: {len(data.get('columns', []))}")
            print(f"   Demo Mode: {data.get('demo_mode', False)}")
            
            if data.get('columns'):
                print(f"   Column Names: {', '.join(data['columns'][:5])}{'...' if len(data['columns']) > 5 else ''}")
            
            if data.get('data') and len(data['data']) > 0:
                print(f"\n📋 Sample Data (first row):")
                first_row = data['data'][0]
                for key, value in list(first_row.items())[:3]:  # Show first 3 fields
                    print(f"   {key}: {value}")
                if len(first_row) > 3:
                    print(f"   ... and {len(first_row) - 3} more fields")
            
        else:
            print(f"❌ Failed to get preview: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing preview: {e}")
    
    # Test with different limit
    print(f"\n2️⃣ Testing with limit=5...")
    
    try:
        response = requests.get(f"{BASE_URL}/metadata/preview/{plant_id}/{table_name}?limit=5")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Limited preview retrieved successfully!")
            print(f"   Requested Limit: 5")
            print(f"   Actual Rows: {data.get('total_rows')}")
            
        else:
            print(f"❌ Failed to get limited preview: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing limited preview: {e}")
    
    # Test with non-existent table
    print(f"\n3️⃣ Testing with non-existent table...")
    
    try:
        response = requests.get(f"{BASE_URL}/metadata/preview/{plant_id}/NONEXISTENT_TABLE")
        
        if response.status_code == 404:
            print(f"✅ Correctly returned 404 for non-existent table")
        elif response.status_code == 500:
            print(f"✅ Correctly returned 500 for database error (expected in demo mode)")
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing non-existent table: {e}")
    
    print(f"\n🎉 Preview Endpoint Test Complete!")

if __name__ == "__main__":
    test_preview_endpoint()
