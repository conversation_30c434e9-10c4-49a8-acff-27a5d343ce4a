#!/usr/bin/env python3
"""
Test script to verify mapping persistence and CRUD operations
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_mapping_crud():
    """Test complete mapping CRUD operations"""
    
    print("🧪 Testing Enhanced Mapping CRUD Operations")
    print("=" * 50)
    
    # Test data
    test_mapping = {
        "plant_id": 1,
        "metadata_table": "CSMETA_PROJECT",
        "field_mappings": {
            "PROJECT_NAME": "title",
            "PROJECT_DESC": "content",
            "PROJECT_TYPE": "tag:1"
        },
        "default_values": {
            "title": "Default Project Title",
            "tag:1": "1"
        },
        "extra_fields": {
            "tags": "1,2,3",
            "document_type": "1"
        },
        "mapping_options": {
            "tag_mapping_by_id": True,
            "allow_extra_fields": True,
            "allow_default_values": True
        }
    }
    
    # 1. CREATE - Test saving mapping
    print("\n1️⃣ CREATE - Saving enhanced mapping...")
    try:
        response = requests.post(f"{BASE_URL}/mappings", json=test_mapping)
        if response.status_code == 200:
            result = response.json()
            mapping_id = result.get('id')
            print(f"✅ Mapping created successfully! ID: {mapping_id}")
            print(f"   Features: {result.get('features', {})}")
        else:
            print(f"❌ Failed to create mapping: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"❌ Error creating mapping: {e}")
        return
    
    # 2. READ - Test getting specific mapping
    print(f"\n2️⃣ READ - Getting mapping {mapping_id}...")
    try:
        response = requests.get(f"{BASE_URL}/mapping/{mapping_id}")
        if response.status_code == 200:
            mapping = response.json()
            print(f"✅ Mapping retrieved successfully!")
            print(f"   Table: {mapping['metadata_table']}")
            print(f"   Field Mappings: {mapping['field_mappings']}")
            print(f"   Default Values: {mapping['default_values']}")
            print(f"   Extra Fields: {mapping['extra_fields']}")
        else:
            print(f"❌ Failed to get mapping: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error getting mapping: {e}")
    
    # 3. READ ALL - Test getting all mappings
    print(f"\n3️⃣ READ ALL - Getting all mappings...")
    try:
        response = requests.get(f"{BASE_URL}/mappings")
        if response.status_code == 200:
            mappings = response.json()
            print(f"✅ Retrieved {len(mappings)} mappings")
            for m in mappings:
                print(f"   - ID: {m['id']}, Plant: {m['plant_id']}, Table: {m['metadata_table']}")
        else:
            print(f"❌ Failed to get all mappings: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error getting all mappings: {e}")
    
    # 4. UPDATE - Test updating mapping
    print(f"\n4️⃣ UPDATE - Updating mapping {mapping_id}...")
    update_data = {
        "field_mappings": {
            "PROJECT_NAME": "title",
            "PROJECT_DESC": "content",
            "PROJECT_TYPE": "tag:2",  # Changed tag
            "PROJECT_STATUS": "custom_status"  # Added field
        },
        "default_values": {
            "title": "Updated Default Title",
            "custom_status": "Active"
        }
    }
    try:
        response = requests.put(f"{BASE_URL}/mapping/{mapping_id}", json=update_data)
        if response.status_code == 200:
            updated_mapping = response.json()
            print(f"✅ Mapping updated successfully!")
            print(f"   Updated Field Mappings: {updated_mapping['field_mappings']}")
            print(f"   Updated Default Values: {updated_mapping['default_values']}")
        else:
            print(f"❌ Failed to update mapping: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error updating mapping: {e}")
    
    # 5. DELETE - Test deleting mapping
    print(f"\n5️⃣ DELETE - Deleting mapping {mapping_id}...")
    try:
        response = requests.delete(f"{BASE_URL}/mapping/{mapping_id}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Mapping deleted successfully!")
            print(f"   Message: {result.get('message')}")
        else:
            print(f"❌ Failed to delete mapping: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error deleting mapping: {e}")
    
    # 6. VERIFY DELETE - Test getting deleted mapping
    print(f"\n6️⃣ VERIFY DELETE - Trying to get deleted mapping {mapping_id}...")
    try:
        response = requests.get(f"{BASE_URL}/mapping/{mapping_id}")
        if response.status_code == 404:
            print(f"✅ Mapping properly deleted (404 Not Found)")
        else:
            print(f"❌ Mapping still exists: {response.status_code}")
    except Exception as e:
        print(f"❌ Error verifying deletion: {e}")
    
    print(f"\n🎉 Enhanced Mapping CRUD Test Complete!")

if __name__ == "__main__":
    test_mapping_crud()
