#!/usr/bin/env python3
"""
Test script for document path resolution functionality
"""

from app.document_resolver import DocumentPathResolver

def test_path_resolution():
    """Test various PP database path formats"""
    
    # Create resolver for Plant1
    resolver = DocumentPathResolver("/Plant1")
    
    test_cases = [
        # Windows UNC paths
        ("\\\\server\\share\\plant1\\docs\\file.pdf", "/Plant1/docs/file.pdf"),
        ("\\\\fileserver\\documents\\plant1\\projects\\spec.pdf", "/Plant1/projects/spec.pdf"),
        
        # Windows drive paths
        ("C:\\PP\\Plant1\\Documents\\manual.pdf", "/Plant1/Documents/manual.pdf"),
        ("D:\\Data\\Plant1\\Files\\drawing.dwg", "/Plant1/Files/drawing.dwg"),
        
        # Unix paths
        ("/opt/pp/plant1/documents/report.pdf", "/Plant1/documents/report.pdf"),
        ("/data/plant1/files/specification.docx", "/Plant1/files/specification.docx"),
        
        # Complex nested paths
        ("\\\\server\\share\\manufacturing\\plant1\\quality\\procedures\\sop_001.pdf", 
         "/Plant1/quality/procedures/sop_001.pdf"),
        
        # Edge cases
        ("file.pdf", "/Plant1/file.pdf"),
        ("", "/Plant1/"),
    ]
    
    print("🧪 Testing Document Path Resolution")
    print("=" * 60)
    
    for original_path, expected_path in test_cases:
        resolved_path, file_exists = resolver.resolve_document_path(original_path)
        
        status = "✅ PASS" if resolved_path == expected_path else "❌ FAIL"
        
        print(f"{status}")
        print(f"  Original: {original_path}")
        print(f"  Expected: {expected_path}")
        print(f"  Resolved: {resolved_path}")
        print(f"  Exists:   {file_exists}")
        print()

def test_multiple_plants():
    """Test resolvers for different plants"""
    
    plants = [
        ("/Plant1", "\\\\server\\plant1\\docs\\file1.pdf"),
        ("/Plant2", "\\\\server\\plant2\\docs\\file2.pdf"),
        ("/Plant3", "/opt/plant3/documents/file3.pdf"),
    ]
    
    print("🏭 Testing Multiple Plant Configurations")
    print("=" * 60)
    
    for storage_path, test_path in plants:
        resolver = DocumentPathResolver(storage_path)
        resolved_path, file_exists = resolver.resolve_document_path(test_path)
        
        print(f"Plant Storage: {storage_path}")
        print(f"  Original:  {test_path}")
        print(f"  Resolved:  {resolved_path}")
        print(f"  Mounted:   {resolver.validate_storage_mount()}")
        print()

def test_storage_validation():
    """Test storage mount validation"""
    
    print("💾 Testing Storage Validation")
    print("=" * 60)
    
    # Test with current directory (should exist)
    resolver = DocumentPathResolver(".")
    stats = resolver.get_storage_stats()
    
    print("Current Directory Test:")
    print(f"  Path: {resolver.plant_storage_path}")
    print(f"  Mounted: {stats.get('mounted', False)}")
    if 'total_gb' in stats:
        print(f"  Total: {stats['total_gb']} GB")
        print(f"  Used: {stats['used_gb']} GB")
        print(f"  Free: {stats['free_gb']} GB")
        print(f"  Usage: {stats['usage_percent']}%")
    if 'error' in stats:
        print(f"  Error: {stats['error']}")
    print()
    
    # Test with non-existent directory
    resolver_invalid = DocumentPathResolver("/nonexistent/path")
    stats_invalid = resolver_invalid.get_storage_stats()
    
    print("Non-existent Path Test:")
    print(f"  Path: {resolver_invalid.plant_storage_path}")
    print(f"  Mounted: {stats_invalid.get('mounted', False)}")
    if 'error' in stats_invalid:
        print(f"  Error: {stats_invalid['error']}")
    print()

if __name__ == "__main__":
    print("🚀 Document Resolver Test Suite")
    print("=" * 60)
    print()
    
    test_path_resolution()
    test_multiple_plants()
    test_storage_validation()
    
    print("✅ All tests completed!")
    print()
    print("💡 Usage in production:")
    print("   1. Configure plant storage paths in UI (e.g., /Plant1, /Plant2)")
    print("   2. Mount host directories to these paths in Docker")
    print("   3. System automatically resolves PP paths to Docker paths")
    print("   4. Paperless imports documents from resolved paths")
