import React, { useEffect, useState } from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'
import { Card, Button, Input, Table, LoadingSpinner, Badge } from '../components/ui'
import { Factory, Plus, Check, Database, Info, Edit, Trash2 } from 'lucide-react'

export default function Plants() {
  const [plants, setPlants] = useState([])
  const [form, setForm] = useState({
    name: '',
    host: '',
    port: '1521',
    service_name: '',
    username: '',
    password: '',
    storage_path: '',
    csdocs_schema: '',
    metadata_schema: ''
  })
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [errors, setErrors] = useState({})
  const [editingPlant, setEditingPlant] = useState(null)
  const [editForm, setEditForm] = useState({})
  const [editSubmitting, setEditSubmitting] = useState(false)
  const [testingConnection, setTestingConnection] = useState(null)
  const [connectionResults, setConnectionResults] = useState({})
  const [showSchemas, setShowSchemas] = useState({})

  const { selectedPlant, setSelectedPlant } = useStore()

  useEffect(() => {
    load()
  }, [])

  async function load() {
    setLoading(true)
    try {
      const r = await api.get('/plants')
      setPlants(r.data)
    } catch (e) {
      console.error(e)
    } finally {
      setLoading(false)
    }
  }

  function handleChange(e) {
    const { name, value } = e.target
    setForm({ ...form, [name]: value })
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
  }

  function validateForm() {
    const newErrors = {}
    if (!form.name.trim()) newErrors.name = 'Name is required'
    if (!form.host.trim()) newErrors.host = 'Host is required'
    if (!form.port.trim()) newErrors.port = 'Port is required'
    if (!form.username.trim()) newErrors.username = 'Username is required'
    if (!form.password.trim()) newErrors.password = 'Password is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  async function handleSubmit(e) {
    e.preventDefault()

    if (!validateForm()) return

    setSubmitting(true)
    try {
      await api.post('/plants', form)
      setForm({
        name: '',
        host: '',
        port: '1521',
        service_name: '',
        username: '',
        password: '',
        storage_path: '',
        csdocs_schema: '',
        metadata_schema: ''
      })
      setErrors({})
      await load()
    } catch (e) {
      console.error(e)
      setErrors({ submit: 'Failed to add plant. Please try again.' })
    } finally {
      setSubmitting(false)
    }
  }

  function handleSelectPlant(plant) {
    setSelectedPlant(plant)
  }

  const handleTestConnection = async (plantId) => {
    setTestingConnection(plantId)
    try {
      const response = await api.post(`/plants/${plantId}/test-connection`)
      setConnectionResults(prev => ({
        ...prev,
        [plantId]: response.data
      }))
    } catch (error) {
      setConnectionResults(prev => ({
        ...prev,
        [plantId]: {
          success: false,
          message: error.response?.data?.detail || 'Connection test failed'
        }
      }))
    } finally {
      setTestingConnection(null)
    }
  }

  const handleDiscoverSchemas = async (plantId) => {
    try {
      const response = await api.get(`/plants/${plantId}/schemas`)
      setShowSchemas(prev => ({
        ...prev,
        [plantId]: response.data
      }))
    } catch (error) {
      console.error('Schema discovery failed:', error)
    }
  }

  const handleDeletePlant = async (plantId) => {
    if (!confirm('Are you sure you want to delete this plant? This will also delete all associated mappings.')) {
      return
    }

    try {
      await api.delete(`/plants/${plantId}`)
      // Refresh plants list
      const response = await api.get('/plants')
      setPlants(response.data)

      // If deleted plant was selected, clear selection
      if (selectedPlant?.id === plantId) {
        setSelectedPlant(null)
      }
    } catch (error) {
      console.error('Delete plant failed:', error)
    }
  }

  const handleEditPlant = (plant) => {
    setEditingPlant(plant)
    setEditForm({
      name: plant.name || '',
      host: plant.host || '',
      port: plant.port || '1521',
      service_name: plant.service_name || '',
      username: plant.username || '',
      password: '', // Don't pre-fill password for security
      storage_path: plant.storage_path || '',
      csdocs_schema: plant.csdocs_schema || '',
      metadata_schema: plant.metadata_schema || '',
      notes: plant.notes || ''
    })
  }

  const handleEditFormChange = (e) => {
    const { name, value } = e.target
    setEditForm(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleEditSubmit = async (e) => {
    e.preventDefault()
    setEditSubmitting(true)

    try {
      await api.put(`/plants/${editingPlant.id}`, editForm)

      // Refresh plants list
      const response = await api.get('/plants')
      setPlants(response.data)

      // Close modal
      setEditingPlant(null)
      setEditForm({})

    } catch (error) {
      console.error('Update plant failed:', error)
    } finally {
      setEditSubmitting(false)
    }
  }

  const handleCancelEdit = () => {
    setEditingPlant(null)
    setEditForm({})
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
          <Factory className="w-5 h-5 text-primary-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Plant Management</h1>
          <p className="text-gray-600">Configure database connections for your plants</p>
        </div>
      </div>

      {/* Add Plant Form */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center space-x-2">
            <Plus className="w-5 h-5" />
            <span>Add New Plant</span>
          </Card.Title>
        </Card.Header>
        <Card.Body>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Input
                label="Plant Name"
                name="name"
                value={form.name}
                onChange={handleChange}
                placeholder="Enter plant name"
                error={errors.name}
                required
              />
              <Input
                label="Host"
                name="host"
                value={form.host}
                onChange={handleChange}
                placeholder="database.example.com"
                error={errors.host}
                required
              />
              <Input
                label="Port"
                name="port"
                type="number"
                value={form.port}
                onChange={handleChange}
                placeholder="1521"
                error={errors.port}
                required
              />
              <Input
                label="Service Name"
                name="service_name"
                value={form.service_name}
                onChange={handleChange}
                placeholder="ORCL (optional)"
                error={errors.service_name}
              />
              <Input
                label="Username"
                name="username"
                value={form.username}
                onChange={handleChange}
                placeholder="Database username"
                error={errors.username}
                required
              />
              <Input
                label="CSDOCS Schema"
                name="csdocs_schema"
                value={form.csdocs_schema}
                onChange={handleChange}
                placeholder="Schema containing CSDOCS table (optional)"
                error={errors.csdocs_schema}
              />
              <Input
                label="Metadata Schema"
                name="metadata_schema"
                value={form.metadata_schema}
                onChange={handleChange}
                placeholder="Schema containing CSMETA_* tables (optional)"
                error={errors.metadata_schema}
              />
              <Input
                label="Password"
                name="password"
                type="password"
                value={form.password}
                onChange={handleChange}
                placeholder="Database password"
                error={errors.password}
                required
              />
              <Input
                label="Storage Path"
                name="storage_path"
                value={form.storage_path}
                onChange={handleChange}
                placeholder="/Plant1 (Docker mount path)"
                error={errors.storage_path}
              />
            </div>

            {/* Docker Storage Configuration Help */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Info className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-800">Docker Storage Configuration</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Specify the Docker mount path where this plant's documents are accessible.
                    This should match your Docker volume mount configuration.
                  </p>
                  <div className="text-xs text-green-600 mt-2">
                    <strong>Example:</strong> Storage Path: "/Plant1" (maps to docker volume mount)
                  </div>
                </div>
              </div>
            </div>

            {/* Schema Configuration Help */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-800">Cross-Schema Configuration</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    If your CSDOCS table and metadata tables (CSMETA_*) are in different Oracle schemas,
                    specify them above. Leave blank to use the connection username as the schema.
                  </p>
                  <div className="text-xs text-blue-600 mt-2">
                    <strong>Example:</strong> CSDOCS Schema: "DOCUMENTS", Metadata Schema: "BUSINESS"
                  </div>
                </div>
              </div>
            </div>

            {errors.submit && (
              <div className="text-sm text-danger-600 bg-danger-50 p-3 rounded-lg">
                {errors.submit}
              </div>
            )}

            <div className="flex justify-end">
              <Button
                type="submit"
                loading={submitting}
                disabled={submitting}
                className="flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Add Plant</span>
              </Button>
            </div>
          </form>
        </Card.Body>
      </Card>

      {/* Plants List */}
      <Card>
        <Card.Header>
          <div className="flex items-center justify-between">
            <Card.Title className="flex items-center space-x-2">
              <Database className="w-5 h-5" />
              <span>Configured Plants</span>
            </Card.Title>
            <Badge variant="info">{plants.length} plants</Badge>
          </div>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
              <span className="ml-3 text-gray-600">Loading plants...</span>
            </div>
          ) : plants.length === 0 ? (
            <div className="text-center py-12">
              <Factory className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No plants configured</h3>
              <p className="text-gray-600">Add your first plant connection above to get started.</p>
            </div>
          ) : (
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Plant Name</Table.HeaderCell>
                  <Table.HeaderCell>Connection</Table.HeaderCell>
                  <Table.HeaderCell>Storage</Table.HeaderCell>
                  <Table.HeaderCell>Schemas</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {plants.map((plant) => (
                  <Table.Row key={plant.id}>
                    <Table.Cell>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Factory className="w-4 h-4 text-primary-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{plant.name}</div>
                          {selectedPlant?.id === plant.id && (
                            <div className="text-xs text-primary-600 font-medium">Currently selected</div>
                          )}
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="text-sm">
                        <div className="font-mono text-gray-900">{plant.host}:{plant.port}</div>
                        <div className="text-gray-500">{plant.service_name || 'No service name'}</div>
                        <div className="text-xs text-gray-400">User: {plant.username}</div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="text-sm">
                        <div className="font-mono text-gray-900">{plant.storage_path || 'Not configured'}</div>
                        {plant.storage_path && (
                          <div className="text-xs text-gray-500">Docker mount path</div>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="text-sm">
                        <div className="text-gray-900">
                          CSDOCS: <span className="font-mono text-xs">{plant.csdocs_schema || plant.username}</span>
                        </div>
                        <div className="text-gray-900">
                          Metadata: <span className="font-mono text-xs">{plant.metadata_schema || plant.username}</span>
                        </div>
                        <Button
                          size="xs"
                          variant="outline"
                          onClick={() => handleDiscoverSchemas(plant.id)}
                          className="mt-1"
                        >
                          Discover
                        </Button>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="space-y-1">
                        {connectionResults[plant.id] ? (
                          <Badge variant={connectionResults[plant.id].success ? "success" : "danger"}>
                            {connectionResults[plant.id].success ? "Connected" : "Failed"}
                          </Badge>
                        ) : (
                          <Badge variant="secondary">Unknown</Badge>
                        )}
                        <Button
                          size="xs"
                          variant="outline"
                          onClick={() => handleTestConnection(plant.id)}
                          disabled={testingConnection === plant.id}
                          className="block"
                        >
                          {testingConnection === plant.id ? "Testing..." : "Test"}
                        </Button>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant={selectedPlant?.id === plant.id ? "success" : "secondary"}
                          onClick={() => handleSelectPlant(plant)}
                          className="flex items-center space-x-1"
                        >
                          {selectedPlant?.id === plant.id ? (
                            <>
                              <Check className="w-3 h-3" />
                              <span>Selected</span>
                            </>
                          ) : (
                            <>
                              <Database className="w-3 h-3" />
                              <span>Select</span>
                            </>
                          )}
                        </Button>
                        <Button
                          size="xs"
                          variant="outline"
                          onClick={() => handleEditPlant(plant)}
                          className="flex items-center space-x-1"
                        >
                          <Edit className="w-3 h-3" />
                        </Button>
                        <Button
                          size="xs"
                          variant="danger"
                          onClick={() => handleDeletePlant(plant.id)}
                          className="flex items-center space-x-1"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Schema Discovery Results */}
      {Object.keys(showSchemas).length > 0 && (
        <Card>
          <Card.Header>
            <Card.Title className="flex items-center space-x-2">
              <Database className="w-5 h-5" />
              <span>Schema Discovery Results</span>
            </Card.Title>
          </Card.Header>
          <Card.Body>
            {Object.entries(showSchemas).map(([plantId, schemaData]) => (
              <div key={plantId} className="mb-6 last:mb-0">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {schemaData.plant_name}
                  </h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowSchemas(prev => {
                      const newState = { ...prev }
                      delete newState[plantId]
                      return newState
                    })}
                  >
                    Close
                  </Button>
                </div>

                <div className="grid gap-4">
                  {schemaData.schemas?.map((schema) => (
                    <div key={schema.schema_name} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">
                          Schema: {schema.schema_name}
                        </h4>
                        <Badge variant="info">
                          {schema.table_count} tables
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {schema.tables?.map((table) => (
                          <div key={table.table_name} className="bg-gray-50 rounded p-3">
                            <div className="font-mono text-sm font-medium text-gray-900">
                              {table.table_name}
                            </div>
                            
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </Card.Body>
        </Card>
      )}

      {/* Edit Plant Modal */}
      {editingPlant && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Edit Plant</h2>
              <p className="text-gray-600 mt-1">Update plant connection details</p>
            </div>

            <form onSubmit={handleEditSubmit} className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Plant Name"
                  name="name"
                  value={editForm.name || ''}
                  onChange={handleEditFormChange}
                  required
                />
                <Input
                  label="Host"
                  name="host"
                  value={editForm.host || ''}
                  onChange={handleEditFormChange}
                  placeholder="localhost or IP address"
                  required
                />
                <Input
                  label="Port"
                  name="port"
                  value={editForm.port || ''}
                  onChange={handleEditFormChange}
                  placeholder="1521"
                  required
                />
                <Input
                  label="Service Name"
                  name="service_name"
                  value={editForm.service_name || ''}
                  onChange={handleEditFormChange}
                  placeholder="ORCL"
                />
                <Input
                  label="Username"
                  name="username"
                  value={editForm.username || ''}
                  onChange={handleEditFormChange}
                  required
                />
                <Input
                  label="Password"
                  name="password"
                  type="password"
                  value={editForm.password || ''}
                  onChange={handleEditFormChange}
                  placeholder="Leave blank to keep current password"
                />
                <Input
                  label="Storage Path"
                  name="storage_path"
                  value={editForm.storage_path || ''}
                  onChange={handleEditFormChange}
                  placeholder="/Plant1 (Docker mount path)"
                />
                <Input
                  label="CSDOCS Schema"
                  name="csdocs_schema"
                  value={editForm.csdocs_schema || ''}
                  onChange={handleEditFormChange}
                  placeholder="Leave blank to use username"
                />
                <Input
                  label="Metadata Schema"
                  name="metadata_schema"
                  value={editForm.metadata_schema || ''}
                  onChange={handleEditFormChange}
                  placeholder="Leave blank to use username"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  name="notes"
                  value={editForm.notes || ''}
                  onChange={handleEditFormChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Optional notes about this plant"
                />
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelEdit}
                  disabled={editSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={editSubmitting}
                  className="flex items-center space-x-2"
                >
                  {editSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" />
                      <span>Updating...</span>
                    </>
                  ) : (
                    <span>Update Plant</span>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
