import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import Navigation from './components/Navigation'
import Dashboard from './pages/Dashboard'
import Plants from './pages/Plants'
import Csdocs from './pages/Csdocs'
import Mapping from './pages/Mapping'
import MappingManager from './pages/MappingManager'
import Migrations from './pages/Migrations'
import Paperless from './pages/Paperless'

export default function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex h-screen">
        <Navigation isOpen={sidebarOpen} onToggle={toggleSidebar} />

        <main className="flex-1 lg:ml-0 overflow-auto">
          <div className="p-4 sm:p-6 lg:p-8 pt-16 lg:pt-8">
            <div className="max-w-7xl mx-auto">
              <Routes>
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/" element={<Csdocs />} />
                <Route path="/mapping" element={<Mapping />} />
                <Route path="/mapping-manager" element={<MappingManager />} />
                <Route path="/migrations" element={<Migrations />} />
                <Route path="/plants" element={<Plants />} />
                <Route path="/paperless" element={<Paperless />} />
              </Routes>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
