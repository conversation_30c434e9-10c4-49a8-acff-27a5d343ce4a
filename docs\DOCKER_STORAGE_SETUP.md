# Docker Storage Setup for Document Migration

## Overview

This guide explains how to configure Docker storage mounts for the GED to TechDoc migration system, where documents from multiple plants are mounted as separate volumes.

## Architecture

```
Docker Host
├── /host/plant1/documents/     → Mounted as /Plant1 in container
├── /host/plant2/documents/     → Mounted as /Plant2 in container  
├── /host/plant3/documents/     → Mounted as /Plant3 in container
└── ...
```

## Docker Compose Configuration

### Example docker-compose.yml

```yaml
version: '3.8'

services:
  # TechDoc (Paperless-NGX)
  paperless:
    image: ghcr.io/paperless-ngx/paperless-ngx:latest
    container_name: paperless
    restart: unless-stopped
    depends_on:
      - db
      - redis
    ports:
      - "8000:8000"
    volumes:
      - paperless_data:/usr/src/paperless/data
      - paperless_media:/usr/src/paperless/media
      - paperless_export:/usr/src/paperless/export
      - paperless_consume:/usr/src/paperless/consume
      # Plant document mounts for import
      - /host/plant1/documents:/Plant1:ro
      - /host/plant2/documents:/Plant2:ro
      - /host/plant3/documents:/Plant3:ro
    environment:
      PAPERLESS_REDIS: redis://redis:6379
      PAPERLESS_DBHOST: db
      PAPERLESS_DBNAME: paperless
      PAPERLESS_DBUSER: paperless
      PAPERLESS_DBPASS: paperless
      PAPERLESS_SECRET_KEY: change-me
      PAPERLESS_URL: http://localhost:8000
      PAPERLESS_ALLOWED_HOSTS: localhost,127.0.0.1
      PAPERLESS_CORS_ALLOWED_HOSTS: http://localhost:8000,http://127.0.0.1:8000
      PAPERLESS_OCR_LANGUAGE: eng
      PAPERLESS_TIME_ZONE: UTC

  # Migration Tool
  migration-tool:
    build: .
    container_name: ged-to-techdoc-migration
    restart: unless-stopped
    depends_on:
      - paperless
      - db
    ports:
      - "8001:8001"  # Backend API
      - "5174:5174"  # Frontend
    volumes:
      - migration_data:/app/data
      # Same plant document mounts as Paperless
      - /host/plant1/documents:/Plant1:ro
      - /host/plant2/documents:/Plant2:ro
      - /host/plant3/documents:/Plant3:ro
    environment:
      DATABASE_URL: sqlite:///./data/app.db
      PAPERLESS_API_URL: http://paperless:8000/api
      PAPERLESS_API_TOKEN: your-paperless-api-token
      DEMO_MODE: 0

  # Database
  db:
    image: postgres:15
    restart: unless-stopped
    volumes:
      - pgdata:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: paperless
      POSTGRES_USER: paperless
      POSTGRES_PASSWORD: paperless

  # Redis
  redis:
    image: redis:7
    restart: unless-stopped

volumes:
  paperless_data:
  paperless_media:
  paperless_export:
  paperless_consume:
  migration_data:
  pgdata:
```

## Plant Configuration

### In the Migration Tool UI

When adding plants, configure the storage paths:

```
Plant 1:
- Name: Manufacturing Plant A
- Storage Path: /Plant1
- Oracle connection details...

Plant 2:
- Name: Distribution Center B  
- Storage Path: /Plant2
- Oracle connection details...

Plant 3:
- Name: Research Facility C
- Storage Path: /Plant3
- Oracle connection details...
```

### Path Resolution Examples

The system automatically resolves PP database paths to Docker mount paths:

```
PP Database Path                    → Docker Mount Path
\\server\share\plant1\docs\file.pdf → /Plant1/docs/file.pdf
/opt/pp/plant2/documents/spec.pdf   → /Plant2/documents/spec.pdf
C:\PP\Plant3\Files\manual.pdf       → /Plant3/Files/manual.pdf
```

## Directory Structure Examples

### Host Directory Structure
```
/host/
├── plant1/
│   └── documents/
│       ├── projects/
│       │   ├── spec_001.pdf
│       │   └── design_002.dwg
│       ├── equipment/
│       │   └── manual_003.pdf
│       └── safety/
│           └── protocol_004.docx
├── plant2/
│   └── documents/
│       ├── maintenance/
│       │   └── log_005.xlsx
│       └── drawings/
│           └── layout_006.dwg
└── plant3/
    └── documents/
        └── research/
            └── report_007.pdf
```

### Container Mount Points
```
Container: /Plant1/
├── projects/
│   ├── spec_001.pdf
│   └── design_002.dwg
├── equipment/
│   └── manual_003.pdf
└── safety/
    └── protocol_004.docx

Container: /Plant2/
├── maintenance/
│   └── log_005.xlsx
└── drawings/
    └── layout_006.dwg

Container: /Plant3/
└── research/
    └── report_007.pdf
```

## Migration Process

### 1. Document Discovery
- System queries PP Oracle database for CSDOC entries
- Extracts original document paths from DOCPATH column
- Resolves paths to Docker mount locations

### 2. Path Validation
- Checks if resolved Docker paths exist
- Reports file availability status in CSDOCS browser
- Validates storage mount accessibility

### 3. Document Import
- Uses resolved Docker paths for Paperless import
- Preserves original metadata from PP database
- Maps PP metadata fields to Paperless document fields

## Troubleshooting

### Common Issues

1. **"Storage path not accessible"**
   ```bash
   # Check if mount exists
   docker exec migration-tool ls -la /Plant1
   
   # Check host directory permissions
   ls -la /host/plant1/documents
   ```

2. **"Document not found"**
   ```bash
   # Verify file exists on host
   ls -la /host/plant1/documents/projects/spec_001.pdf
   
   # Check container mount
   docker exec migration-tool ls -la /Plant1/projects/spec_001.pdf
   ```

3. **"Permission denied"**
   ```bash
   # Fix host directory permissions
   sudo chown -R 1000:1000 /host/plant1/documents
   sudo chmod -R 755 /host/plant1/documents
   ```

### Debug Commands

```bash
# Check storage mounts in container
docker exec migration-tool df -h

# List plant directories
docker exec migration-tool ls -la /Plant*

# Test document path resolution
curl -X POST http://localhost:8001/plants/1/test-document-path \
  -H "Content-Type: application/json" \
  -d '{"docpath": "\\\\server\\share\\plant1\\docs\\file.pdf"}'

# Get storage information
curl http://localhost:8001/plants/1/storage
```

## Security Considerations

### Read-Only Mounts
- Mount plant directories as read-only (`:ro`) to prevent accidental modifications
- Migration tool only needs read access to source documents

### File Permissions
- Ensure consistent UID/GID mapping between host and container
- Use proper file permissions (755 for directories, 644 for files)

### Network Security
- Restrict access to Oracle databases using network policies
- Use secure connections for database access
- Implement proper API authentication

## Performance Optimization

### Storage Performance
- Use SSD storage for better I/O performance
- Consider NFS or network storage for large document volumes
- Monitor disk space usage across all plant mounts

### Container Resources
```yaml
services:
  migration-tool:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

### Caching
- Enable Docker BuildKit for faster builds
- Use volume caching for frequently accessed data
- Implement application-level caching for metadata

## Monitoring

### Health Checks
```yaml
services:
  migration-tool:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### Storage Monitoring
- Monitor disk usage: `docker exec migration-tool df -h`
- Check mount status: `docker exec migration-tool mount | grep Plant`
- Validate file accessibility: API endpoint `/plants/{id}/storage`

This setup provides a robust, scalable solution for migrating documents from multiple plants while maintaining proper isolation and security.
