import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  FileText,
  GitBranch,
  Database,
  Factory,
  FileSearch,
  Menu,
  X
} from 'lucide-react'
import { useStore } from '../store/useStore'
import PlantSelector from './PlantSelector'

const Navigation = ({ isOpen, onToggle }) => {
  const location = useLocation()
  const selectedPlant = useStore(s => s.selectedPlant)
  
  const navigationItems = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: Database,
      description: 'Migration progress overview'
    },
    {
      name: 'CSDOCS Browser',
      path: '/',
      icon: FileText,
      description: 'Browse and search documents'
    },
    {
      name: 'Field Mapping',
      path: '/mapping',
      icon: GitBranch,
      description: 'Map database fields to TechDoc'
    },
    {
      name: 'Manage Mappings',
      path: '/mapping-manager',
      icon: GitBranch,
      description: 'View and manage existing mappings'
    },
    {
      name: 'Migrations',
      path: '/migrations',
      icon: Database,
      description: 'Run and monitor data migrations'
    },
    {
      name: 'Plants',
      path: '/plants',
      icon: Factory,
      description: 'Manage plant connections'
    },
    {
      name: 'TechDoc Config',
      path: '/paperless',
      icon: FileSearch,
      description: 'Configure TechDoc connection'
    }
  ]
  
  const isActivePath = (path) => {
    return location.pathname === path
  }
  
  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={onToggle}
          className="p-2 rounded-lg bg-white shadow-md border border-gray-200 text-gray-600 hover:text-gray-900"
        >
          {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
        </button>
      </div>
      
      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={onToggle}
        />
      )}
      
      {/* Sidebar */}
      <aside className={`
        fixed lg:static inset-y-0 left-0 z-40 w-80 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <FileSearch className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">GED → TechDoc</h1>
                <p className="text-sm text-gray-500">Migration Tool</p>
              </div>
            </div>
            
            {/* Plant Selector */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selected Plant
              </label>
              <PlantSelector />
            </div>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto scrollbar-thin">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const isActive = isActivePath(item.path)
              
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => onToggle && onToggle()}
                  className={`
                    nav-link
                    ${isActive ? 'nav-link-active' : 'nav-link-inactive'}
                  `}
                >
                  <Icon className="w-5 h-5 mr-3 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{item.name}</div>
                    <div className="text-xs text-gray-500 truncate">{item.description}</div>
                  </div>
                  {isActive && (
                    <div className="w-2 h-2 bg-primary-600 rounded-full ml-2"></div>
                  )}
                </Link>
              )
            })}
          </nav>
          
          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              <p>Version 8.3</p>
              <p className="mt-1">© 2025 GED to TechDoc</p>
            </div>
          </div>
        </div>
      </aside>
    </>
  )
}

export default Navigation
