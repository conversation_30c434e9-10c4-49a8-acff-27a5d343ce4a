# Enhanced Mapping Features

## 🎯 **New Mapping Capabilities**

The field mapping system has been enhanced with three powerful new features:

### **1. Tags as IDs** 🏷️
- **Problem**: Tags were mapped as text, causing duplicates and inconsistencies
- **Solution**: Map directly to Paperless tag IDs from the tags table
- **Usage**: Select "Tag: Safety (ID: 3)" from the dropdown
- **Result**: Documents get tagged with the exact tag ID, ensuring consistency

### **2. Extra Paperless Fields** ➕
- **Problem**: Some Paperless fields don't have PP equivalents
- **Solution**: Add Paperless fields with default values even without PP mapping
- **Usage**: Click "Add Field" → Select Paperless field → Set default value
- **Result**: All migrated documents get the specified field value

### **3. Default Values** 🎯
- **Problem**: Some fields need default values when PP data is missing
- **Solution**: Set default values for any field, custom field, or tag
- **Usage**: Enter default value in the "Default Value" column
- **Result**: Uses default when PP column is empty or null

## 🚀 **How to Use Enhanced Mapping**

### **Step 1: Standard Field Mapping**
1. **Select Plant** → Choose your plant from the dropdown
2. **Select Metadata Table** → Choose PP metadata table (CSMETA_PROJECT, etc.)
3. **Map Fields** → Map PP columns to Paperless fields as usual

### **Step 2: Tag Mapping with IDs**
1. **Select Tag Field** → Choose "Tag: [TagName] (ID: [ID])" from dropdown
2. **Map PP Column** → Map PP column that contains tag information
3. **Set Default** → Optionally set default tag ID if PP column is empty
4. **Result** → Documents tagged with exact Paperless tag ID

### **Step 3: Add Extra Fields**
1. **Click "Add Field"** → Opens extra field modal
2. **Select Paperless Field** → Choose from standard fields, tags, or custom fields
3. **Set Default Value** → Enter the value for all documents
4. **Add Field** → Field added to extra fields list
5. **Result** → All documents get this field with the specified value

### **Step 4: Set Default Values**
1. **Map Field** → First map PP column to Paperless field
2. **Enter Default** → Type default value in "Default Value" column
3. **Save Mapping** → Default used when PP column is empty
4. **Result** → No documents left with empty required fields

## 📊 **Enhanced Mapping Interface**

### **New Table Columns**
- **Column Name**: PP metadata column name
- **Data Type**: PP column data type and constraints
- **TechDoc Field**: Enhanced dropdown with tags, types, correspondents by ID
- **Default Value**: Input field for default values (enabled when field is mapped)
- **Status**: Visual indicator of mapping status

### **Enhanced Dropdown Options**
```
Standard Fields
├── title (required)
├── content
├── document_type
└── correspondent

Tags (by ID)
├── Tag: Important (ID: 1)
├── Tag: Project (ID: 2)
├── Tag: Safety (ID: 3)
└── Tag: Maintenance (ID: 4)

Document Types
├── Type: Manual (ID: 1)
├── Type: Procedure (ID: 2)
└── Type: Report (ID: 3)

Correspondents
├── Correspondent: Engineering (ID: 1)
├── Correspondent: Maintenance (ID: 2)
└── Correspondent: Quality (ID: 3)

Custom Fields
├── custom_field_1 (will create)
├── equipment_id (exists)
└── project_code (exists)

Special Mappings
├── Set default value (use default:value)
└── Ignore this field during migration
```

### **Extra Fields Section**
- **Purpose**: Add Paperless fields without PP equivalents
- **Interface**: Clean card with "Add Field" button
- **Management**: Easy add/remove with visual field list
- **Examples**: 
  - Add "Important" tag to all documents
  - Set default document type for all documents
  - Add custom field with plant-specific value

## 🔧 **Technical Implementation**

### **Backend Enhancements**
- **Enhanced Models**: Added `default_values`, `extra_fields`, `mapping_options` columns
- **Enhanced API**: `/paperless/fields` returns tags, document types, correspondents with IDs
- **Enhanced CRUD**: `save_enhanced_mapping()` handles complex mapping data
- **Migration Support**: Database migration adds new columns seamlessly

### **Frontend Enhancements**
- **Enhanced Dropdown**: Shows all Paperless entities with IDs
- **Default Value Inputs**: Per-field default value configuration
- **Extra Fields Modal**: Clean interface for adding extra fields
- **Visual Indicators**: Tag ID mapping indicators and status badges

### **Data Structure**
```json
{
  "field_mappings": {
    "PP_COLUMN": "paperless_field",
    "TAG_COLUMN": "tag:3",
    "TYPE_COLUMN": "document_type:1"
  },
  "default_values": {
    "title": "Default Document Title",
    "tag:3": "3",
    "custom_field_1": "Plant A"
  },
  "extra_fields": {
    "tags": "1,2,3",
    "document_type": "1",
    "custom_plant_code": "PLANT_A"
  },
  "mapping_options": {
    "allow_extra_fields": true,
    "allow_default_values": true,
    "tag_mapping_by_id": true
  }
}
```

## ✅ **Migration Benefits**

### **Data Consistency**
- **Tag IDs**: Eliminates duplicate tags, ensures exact matches
- **Reference IDs**: Document types and correspondents use proper IDs
- **Default Values**: No missing required fields in migrated documents

### **Flexibility**
- **Extra Fields**: Add plant-specific metadata not in PP
- **Default Values**: Handle missing or inconsistent PP data
- **Mixed Mapping**: Combine PP data with default values seamlessly

### **Quality Control**
- **Visual Feedback**: Clear indicators of mapping status and tag ID usage
- **Validation**: Ensures all required fields have values
- **Preview**: See exactly what will be migrated before execution

## 🎉 **Ready for Production**

The enhanced mapping system is now ready for production use with:

- ✅ **Tags mapped by ID** for consistency
- ✅ **Extra Paperless fields** without PP equivalents
- ✅ **Default values** for any field or custom field
- ✅ **Enhanced UI** with visual indicators
- ✅ **Database migration** completed successfully
- ✅ **Backward compatibility** with existing mappings

**Access the enhanced mapping system at: http://localhost:5174/mapping**
